"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.genericSearch = exports.initializePrismaCache = void 0;
const internals_1 = require("@prisma/internals");
const fs_1 = require("fs");
const path_1 = __importDefault(require("path"));
// Cache schema metadata to avoid parsing on every request
let schemaCache = null;
const mergeSchemaFiles = (schemaDir) => {
    const files = (0, fs_1.readdirSync)(schemaDir).filter((file) => file.endsWith(".prisma"));
    return files
        .map((file) => (0, fs_1.readFileSync)(path_1.default.join(schemaDir, file), "utf-8"))
        .join("\n");
};
// Initialize the schema cache
const initSchemaCache = async () => {
    if (schemaCache)
        return schemaCache;
    try {
        const schemaDir = path_1.default.join(process.cwd(), "prisma", "schema");
        // const schemaPath = path.join(process.cwd(), "prisma/schema/schema.prisma");
        const schema = mergeSchemaFiles(schemaDir);
        // readFileSync
        const dmmf = await (0, internals_1.getDMMF)({ datamodel: schema });
        const modelFieldTypes = {};
        const modelRelations = {};
        // Process all models
        dmmf.datamodel.models.forEach((model) => {
            const modelName = model.name.toLowerCase();
            modelFieldTypes[modelName] = {};
            modelRelations[modelName] = [];
            model.fields.forEach((field) => {
                modelFieldTypes[modelName][field.name] = field.type;
                if (field.kind === "object") {
                    modelRelations[modelName].push(field.name);
                }
            });
        });
        // Process enum values
        const enumValues = {};
        dmmf.datamodel.enums.forEach((enumType) => {
            enumValues[enumType.name] = enumType.values.map((v) => v.name);
        });
        schemaCache = { modelFieldTypes, modelRelations, enumValues };
        return schemaCache;
    }
    catch (error) {
        console.error("Failed to initialize schema cache:", error);
        throw error;
    }
};
// Call this function when your application starts
const initializePrismaCache = async () => {
    await initSchemaCache();
};
exports.initializePrismaCache = initializePrismaCache;
const genericSearch = async ({ model, filters = {}, relatedFilters = {}, dateRange, extraConditions = [], page, pageSize, select, userIdFilter, includeRelations, }) => {
    // Ensure schema cache is initialized
    if (!schemaCache) {
        await initSchemaCache();
    }
    const start = Date.now();
    const take = page && pageSize ? pageSize : undefined;
    const skip = page && pageSize ? (page - 1) * pageSize : undefined;
    const searchConditions = [];
    const normalizedModel = model.toLowerCase();
    // Handle related filters
    for (const relation in relatedFilters) {
        const fields = relatedFilters[relation];
        for (const field in fields) {
            const value = fields[field];
            if (value === undefined || value === null || value === "")
                continue;
            const valueList = value.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: valueList.map((values) => ({
                    [relation]: {
                        [field]: {
                            contains: values,
                            mode: "insensitive",
                        },
                    },
                })),
            });
        }
    }
    // Handle date range filters
    if (dateRange?.from || dateRange?.to) {
        const dateCondition = { [dateRange.field]: {} };
        if (dateRange.from)
            dateCondition[dateRange.field].gte = dateRange.from;
        if (dateRange.to)
            dateCondition[dateRange.field].lte = dateRange.to;
        searchConditions.push(dateCondition);
    }
    // Get field types from cache
    const fieldTypeMap = schemaCache?.modelFieldTypes[normalizedModel] || {};
    const enumValues = schemaCache?.enumValues || {};
    // Process filters with cached type information
    for (const field in filters) {
        const value = filters[field];
        if (value === undefined || value === null || value === "")
            continue;
        const fieldType = fieldTypeMap[field];
        if (fieldType === "String") {
            const valueList = value.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: valueList.map((values) => ({
                    [field]: {
                        contains: values,
                        mode: "insensitive",
                    },
                })),
            });
        }
        else if (["Int", "Float", "Decimal"].includes(fieldType)) {
            const num = Number(value);
            if (!isNaN(num)) {
                searchConditions.push({ [field]: num });
            }
        }
        else if (enumValues[fieldType]) {
            // Optimized enum handling
            const possibleValues = enumValues[fieldType];
            const matchingEnumValues = possibleValues.filter((enumValue) => enumValue.toLowerCase().includes(String(value).toLowerCase()));
            if (matchingEnumValues.length > 0) {
                searchConditions.push({
                    [field]: {
                        in: matchingEnumValues,
                    },
                });
            }
            else {
                searchConditions.push({ [field]: value });
            }
        }
        else {
            searchConditions.push({ [field]: value });
        }
    }
    // Build the where clause
    const whereClause = {
        AND: [...searchConditions, ...extraConditions],
        ...(userIdFilter ? { user_id: { in: userIdFilter } } : {}),
    };
    // Prepare include object only if relations exist and are requested
    let include = {};
    if (includeRelations) {
        const modelRelations = schemaCache?.modelRelations[normalizedModel] || [];
        let relationsToInclude;
        if (includeRelations === true) {
            // Include all relations
            relationsToInclude = modelRelations;
        }
        else {
            // Filter to valid relations for the model
            relationsToInclude = includeRelations.filter((rel) => modelRelations.includes(rel));
        }
        relationsToInclude.forEach((relation) => {
            include[relation] = true;
        });
    }
    // Build query options
    let queryOptions = {
        where: whereClause,
        take,
        skip,
        orderBy: { id: "desc" },
    };
    // Add either select or include, but not both
    if (select) {
        queryOptions.select = select;
    }
    else if (Object.keys(include).length > 0 && includeRelations) {
        queryOptions.include = include;
    }
    // Execute query
    const queryStart = Date.now();
    const data = await prisma[model].findMany(queryOptions);
    const queryTime = Date.now() - queryStart;
    const datalength = await prisma[model].count({ where: whereClause });
    return { data, datalength };
};
exports.genericSearch = genericSearch;
//# sourceMappingURL=genericSearch.js.map
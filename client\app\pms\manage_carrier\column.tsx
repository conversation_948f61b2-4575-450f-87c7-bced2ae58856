"use client";
import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
import UpdateCarrier from "./UpdateCarrier";
import DeleteRow from "@/app/_component/DeleteRow";
import { carrier_routes } from "@/lib/routePath";
import { PermissionWrapper } from "@/lib/permissionWrapper";
import { useSearchParams } from "next/navigation";
import { ColDef } from "ag-grid-community";

export interface Carrier {
  name: string;
  register1: string;
  carrier_code: any;
  carrier_2nd_name: string;
  // state: string;
  // city: string;
  // address: string;
  // phone: string;
  // postalcode: string;
  id: any;
}

export const column = (permissions: string[]) => {
  const columns = [
    {
      field: "name",
      headerName: "Carrier Name",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
    },
    {
      field: "carrier_code",
      headerName: "VAP Id",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
    },
    {
      field: "carrier_2nd_name",
      headerName: "Carrier Name 2",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
    },
    {
      field: "createdBy",
      headerName: "Entered By",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
    },
    {
      field: "action",
      headerName: "Action",
      width: 150, // Fixed width
      minWidth: 150, // Minimum width (cannot shrink below this)
      maxWidth: 150, // Maximum width (cannot expand beyond this)
      cellRenderer: (params: any) => {
        const carrier = params?.data;

        return (
          <div className="flex items-center">
            <PermissionWrapper
              permissions={permissions}
              requiredPermissions={["view-carrier"]}
            >
              <UpdateCarrier data={carrier} />
            </PermissionWrapper>

            <PermissionWrapper
              permissions={permissions}
              requiredPermissions={["delete-carrier"]}
            >
              <DeleteRow
                route={`${carrier_routes.DELETE_CARRIER}/${carrier?.id}`}
              />
            </PermissionWrapper>
          </div>
        );
      },
      sortable:false,
      cellStyle: {
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center', // Fallback
      },
    },
  ];
  return columns;
};

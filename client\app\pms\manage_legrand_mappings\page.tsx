import { AdminNavBar } from "@/components/adminNavBar/adminNavBar";
import AddLegrandMapping from "./AddLegrandMapping";
import { getAllData, getCookie } from "@/lib/helpers";
import { employee_routes, legrandMapping_routes } from "@/lib/routePath";
import ViewLegrandMappings from "./ViewLegrandMappings";
import { get } from "node:https";
import { PermissionWrapper } from "@/lib/permissionWrapper";

const LegrandMappingsPage = async ({
  searchParams,
}: {
  searchParams: {
    pageSize?: string;
    page?: string;
    "Business Unit"?: string;
    "Legal Name"?: string;
    "Custome Code"?: string;
    "Shipping Billing Name"?: string;
    "Shipping Billing Address"?: string;
    "Location"?: string;
    "Zip Postal"?: string;
    "Alias City"?: string;
    "Alias Shipping Names"?: string;
  };
}) => {
  const {
    page = "1",
    pageSize = "50",
    "Business Unit":businessUnit,
    "Legal Name":legalName,
    "Custome Code":customeCode,
    "Shipping Billing Name":shippingBillingName,
    "Shipping Billing Address":shippingBillingAddress,
    "Location":location,
    "Zip Postal":zipPostal,
    "Alias City":aliasCity,
    "Alias Shipping Names":aliasShippingNames,
  } = searchParams;

  const params = new URLSearchParams();

  if (pageSize) params.append("pageSize", pageSize);
  if (page) params.append("page", page);
  if (businessUnit) params.append("businessUnit", businessUnit);
  if (legalName) params.append("legalName", legalName);
  if (customeCode) params.append("customeCode", customeCode);
  if (shippingBillingName)
    params.append("shippingBillingName", shippingBillingName);
  if (shippingBillingAddress)
    params.append("shippingBillingAddress", shippingBillingAddress);
  if (location) params.append("location", location);
  if (zipPostal) params.append("zipPostal", zipPostal);
  if (aliasCity) params.append("aliasCity", aliasCity);
  if (aliasShippingNames) params.append("aliasShippingNames", aliasShippingNames);
  const apiUrl = `${
    legrandMapping_routes.GET_LEGRAND_MAPPINGS
  }?${params.toString()}`;
  const getLegrandMappings = await getAllData(apiUrl);

  const userData = await getAllData(employee_routes.GETCURRENT_USER);
  const userPermissions =
    userData?.role?.role_permission.map(
      (item: any) => item.permission.action
    ) || [];
  const corporationCookie = await getCookie("corporationtoken");

  const permissions = corporationCookie ? ["allow_all"] : userPermissions;

  return (
    <div className="w-full p-2 pl-4">
      <div className="h-9 flex items-center">
        <AdminNavBar
          link={"/pms/manage_legrand_mappings"}
          name={"Manage Legrand Mappings"}
        />
      </div>
      <div className="space-y-2">
        <h1 className="text-2xl">Manage Legrand Mappings</h1>
        <p className="text-sm text-gray-700">
          Here You Can Manage Legrand Mappings
        </p>
      </div>
      <div className="w-full">
        <div className="flex justify-end">
          <PermissionWrapper
            permissions={permissions}
            requiredPermissions={["create-legrand-mappings"]}
          >
            <AddLegrandMapping />
          </PermissionWrapper>
        </div>
        <div className="w-full py-4 animate-in fade-in duration-1000">
          <PermissionWrapper
            permissions={permissions}
            requiredPermissions={["view-legrand-mappings"]}
          >
            <ViewLegrandMappings
              getLegrandMappings={getLegrandMappings}
              permissions={permissions}
            />
          </PermissionWrapper>
        </div>
      </div>
    </div>
  );
};

export default LegrandMappingsPage;

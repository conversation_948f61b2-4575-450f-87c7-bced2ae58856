
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.7.0
 * Query Engine version: 3cff47a7f5d65c3ea74883f1d736e41d68ce91ed
 */
Prisma.prismaVersion = {
  client: "6.7.0",
  engine: "3cff47a7f5d65c3ea74883f1d736e41d68ce91ed"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.ClientCustomFieldArrangementScalarFieldEnum = {
  id: 'id',
  client_id: 'client_id',
  custom_field_id: 'custom_field_id',
  order: 'order'
};

exports.Prisma.CommentScalarFieldEnum = {
  id: 'id',
  content: 'content',
  ticketId: 'ticketId',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedBy: 'updatedBy',
  updatedAt: 'updatedAt',
  deletedBy: 'deletedBy',
  deletedAt: 'deletedAt'
};

exports.Prisma.ManifestDetailsSchemaScalarFieldEnum = {
  id: 'id',
  trackSheetId: 'trackSheetId',
  manifestStatus: 'manifestStatus',
  manifestDate: 'manifestDate',
  manifestNotes: 'manifestNotes',
  actionRequired: 'actionRequired',
  createdAt: 'createdAt',
  createdBy: 'createdBy',
  updatedAt: 'updatedAt',
  updatedBy: 'updatedBy',
  deletedAt: 'deletedAt',
  deletedBy: 'deletedBy'
};

exports.Prisma.ManualMatchingMappingScalarFieldEnum = {
  id: 'id',
  division: 'division',
  company: 'company',
  brandDivisionName: 'brandDivisionName',
  ManualShipment: 'ManualShipment',
  corporationId: 'corporationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PipelineScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  workType: 'workType',
  isActive: 'isActive',
  corporationId: 'corporationId',
  createdAt: 'createdAt',
  createdBy: 'createdBy',
  updatedAt: 'updatedAt',
  updatedBy: 'updatedBy',
  deletedAt: 'deletedAt',
  deletedBy: 'deletedBy'
};

exports.Prisma.PipelineStageScalarFieldEnum = {
  id: 'id',
  pipelineId: 'pipelineId',
  name: 'name',
  description: 'description',
  order: 'order',
  createdAt: 'createdAt',
  createdBy: 'createdBy',
  updatedAt: 'updatedAt',
  updatedBy: 'updatedBy',
  deletedAt: 'deletedAt',
  deletedBy: 'deletedBy'
};

exports.Prisma.SystemGeneratedWarningScalarFieldEnum = {
  id: 'id',
  trackSheetId: 'trackSheetId',
  severity: 'severity',
  message: 'message',
  createdAt: 'createdAt',
  createdBy: 'createdBy',
  updatedAt: 'updatedAt',
  updatedBy: 'updatedBy',
  deletedAt: 'deletedAt',
  deletedBy: 'deletedBy'
};

exports.Prisma.TagScalarFieldEnum = {
  id: 'id',
  tagName: 'tagName',
  color: 'color',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedBy: 'updatedBy',
  updatedAt: 'updatedAt',
  deletedBy: 'deletedBy',
  deletedAt: 'deletedAt'
};

exports.Prisma.TicketScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  owner: 'owner',
  priority: 'priority',
  workItemId: 'workItemId',
  pipelineId: 'pipelineId',
  tags: 'tags',
  currentStageId: 'currentStageId',
  createdAt: 'createdAt',
  createdBy: 'createdBy',
  updatedAt: 'updatedAt',
  updatedBy: 'updatedBy',
  deletedAt: 'deletedAt',
  deletedBy: 'deletedBy'
};

exports.Prisma.TicketStageScalarFieldEnum = {
  id: 'id',
  ticketId: 'ticketId',
  pipelineStageId: 'pipelineStageId',
  assignedTo: 'assignedTo',
  dueAt: 'dueAt',
  createdAt: 'createdAt',
  createdBy: 'createdBy',
  updatedAt: 'updatedAt',
  updatedBy: 'updatedBy',
  deletedAt: 'deletedAt',
  deletedBy: 'deletedBy'
};

exports.Prisma.TrackSheetCustomFieldMappingScalarFieldEnum = {
  id: 'id',
  tracksheetId: 'tracksheetId',
  customFieldId: 'customFieldId',
  value: 'value',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TrackSheetImportScalarFieldEnum = {
  id: 'id',
  fileName: 'fileName',
  status: 'status',
  location: 'location',
  createdAt: 'createdAt',
  createdBy: 'createdBy',
  updatedAt: 'updatedAt',
  updatedBy: 'updatedBy',
  deletedAt: 'deletedAt',
  deletedBy: 'deletedBy'
};

exports.Prisma.TrackSheetImportErrorScalarFieldEnum = {
  id: 'id',
  trackSheetImportId: 'trackSheetImportId',
  rowNumber: 'rowNumber',
  conflictingValue: 'conflictingValue',
  reason: 'reason',
  createdAt: 'createdAt',
  createdBy: 'createdBy',
  updatedAt: 'updatedAt',
  updatedBy: 'updatedBy',
  deletedAt: 'deletedAt',
  deletedBy: 'deletedBy'
};

exports.Prisma.AssociateScalarFieldEnum = {
  id: 'id',
  name: 'name',
  corporation_id: 'corporation_id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.BranchScalarFieldEnum = {
  id: 'id',
  branch_name: 'branch_name',
  corporation_id: 'corporation_id'
};

exports.Prisma.CarrierScalarFieldEnum = {
  id: 'id',
  name: 'name',
  carrier_code: 'carrier_code',
  code: 'code',
  country: 'country',
  carrier_2nd_name: 'carrier_2nd_name',
  created_at: 'created_at',
  updated_at: 'updated_at',
  corporation_id: 'corporation_id',
  createdBy: 'createdBy'
};

exports.Prisma.CategoryScalarFieldEnum = {
  id: 'id',
  category_name: 'category_name',
  corporation_id: 'corporation_id'
};

exports.Prisma.ClientScalarFieldEnum = {
  id: 'id',
  corporation_id: 'corporation_id',
  client_name: 'client_name',
  ownership_id: 'ownership_id',
  owner_name: 'owner_name',
  country: 'country',
  branch_id: 'branch_id',
  associateId: 'associateId',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.ClientCarrierScalarFieldEnum = {
  id: 'id',
  corporation_id: 'corporation_id',
  client_id: 'client_id',
  carrier_id: 'carrier_id',
  created_at: 'created_at',
  updated_at: 'updated_at',
  payment_terms: 'payment_terms'
};

exports.Prisma.ClientFTPFilePathConfigScalarFieldEnum = {
  clientId: 'clientId',
  filePath: 'filePath',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CorporationScalarFieldEnum = {
  corporation_id: 'corporation_id',
  username: 'username',
  email: 'email',
  password: 'password',
  country: 'country',
  state: 'state',
  city: 'city',
  address: 'address',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.CustomFieldScalarFieldEnum = {
  id: 'id',
  type: 'type',
  autoOption: 'autoOption',
  name: 'name',
  createdAt: 'createdAt',
  createdBy: 'createdBy',
  updatedAt: 'updatedAt',
  updatedBy: 'updatedBy'
};

exports.Prisma.DailyPlanningScalarFieldEnum = {
  id: 'id',
  corporation_id: 'corporation_id',
  daily_planning_date: 'daily_planning_date',
  client_id: 'client_id',
  user_id: 'user_id',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.DailyPlanningByTypeScalarFieldEnum = {
  id: 'id',
  daily_planning_id: 'daily_planning_id',
  type: 'type',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.DailyPlanningDetailsScalarFieldEnum = {
  id: 'id',
  corporation_id: 'corporation_id',
  daily_planning_type_id: 'daily_planning_type_id',
  bucket: 'bucket',
  priority_status: 'priority_status',
  user_id: 'user_id',
  daily_planning_id: 'daily_planning_id',
  carrier_id: 'carrier_id',
  invoice_entry_total: 'invoice_entry_total',
  two_ten_error_total: 'two_ten_error_total',
  alloted: 'alloted',
  pending: 'pending',
  review_status: 'review_status',
  pf_status: 'pf_status',
  batch_error: 'batch_error',
  reason: 'reason',
  hold: 'hold',
  two_ten_error: 'two_ten_error',
  two_ten_m_f: 'two_ten_m_f',
  two_ten_success: 'two_ten_success',
  two_ten_hold: 'two_ten_hold',
  two_ten_import_additional: 'two_ten_import_additional',
  two_ten_manual_match: 'two_ten_manual_match',
  old: 'old',
  new: 'new',
  ute: 'ute',
  shipping_type: 'shipping_type',
  division: 'division',
  receive_by: 'receive_by',
  receive_date: 'receive_date',
  review_by: 'review_by',
  review_date: 'review_date',
  reconcile_by: 'reconcile_by',
  reconcile_date: 'reconcile_date',
  send_by: 'send_by',
  send_date: 'send_date',
  no_invoices: 'no_invoices',
  amount_of_invoice: 'amount_of_invoice',
  source: 'source',
  age: 'age',
  correct: 'correct',
  entry: 'entry',
  currency: 'currency',
  notes: 'notes',
  type: 'type',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.ImageFileScalarFieldEnum = {
  id: 'id',
  size: 'size',
  name: 'name',
  path: 'path',
  type: 'type',
  created_at: 'created_at',
  updated_at: 'updated_at',
  user_id: 'user_id',
  corporation_id: 'corporation_id'
};

exports.Prisma.InvoiceFileScalarFieldEnum = {
  id: 'id',
  date: 'date',
  fileName: 'fileName',
  noOfPages: 'noOfPages',
  assignedTo: 'assignedTo',
  carrierId: 'carrierId',
  createdAt: 'createdAt',
  createdBy: 'createdBy',
  updatedAt: 'updatedAt',
  updatedBy: 'updatedBy',
  deletedAt: 'deletedAt',
  deletedBy: 'deletedBy'
};

exports.Prisma.LegrandMappingScalarFieldEnum = {
  id: 'id',
  businessUnit: 'businessUnit',
  legalName: 'legalName',
  customeCode: 'customeCode',
  shippingBillingName: 'shippingBillingName',
  shippingBillingAddress: 'shippingBillingAddress',
  location: 'location',
  zipPostal: 'zipPostal',
  aliasCity: 'aliasCity',
  aliasShippingNames: 'aliasShippingNames',
  corporationId: 'corporationId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RolesScalarFieldEnum = {
  id: 'id',
  name: 'name',
  corporation_id: 'corporation_id',
  client_id: 'client_id',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.PermissionsScalarFieldEnum = {
  id: 'id',
  module: 'module',
  action: 'action',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.RolePermissionScalarFieldEnum = {
  id: 'id',
  role_id: 'role_id',
  permission_id: 'permission_id',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.SessionScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  session_token: 'session_token',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.SuperAdminScalarFieldEnum = {
  id: 'id',
  username: 'username',
  email: 'email',
  password: 'password',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.TicketStageChangeLogScalarFieldEnum = {
  id: 'id',
  ticketId: 'ticketId',
  fromStage: 'fromStage',
  toStage: 'toStage',
  createdAt: 'createdAt',
  createdBy: 'createdBy',
  updatedAt: 'updatedAt',
  updatedBy: 'updatedBy',
  deletedAt: 'deletedAt',
  deletedBy: 'deletedBy'
};

exports.Prisma.TrackSheetsScalarFieldEnum = {
  id: 'id',
  clientId: 'clientId',
  company: 'company',
  division: 'division',
  masterInvoice: 'masterInvoice',
  invoice: 'invoice',
  bol: 'bol',
  invoiceDate: 'invoiceDate',
  receivedDate: 'receivedDate',
  shipmentDate: 'shipmentDate',
  carrierId: 'carrierId',
  invoiceStatus: 'invoiceStatus',
  manualMatching: 'manualMatching',
  invoiceType: 'invoiceType',
  currency: 'currency',
  qtyShipped: 'qtyShipped',
  weightUnitName: 'weightUnitName',
  quantityBilledText: 'quantityBilledText',
  freightClass: 'freightClass',
  invoiceTotal: 'invoiceTotal',
  savings: 'savings',
  financialNotes: 'financialNotes',
  ftpFileName: 'ftpFileName',
  ftpPage: 'ftpPage',
  filePath: 'filePath',
  billToClient: 'billToClient',
  docAvailable: 'docAvailable',
  notes: 'notes',
  mistake: 'mistake',
  freightTerm: 'freightTerm',
  shipperAddressType: 'shipperAddressType',
  consigneeAddressType: 'consigneeAddressType',
  billToAddressType: 'billToAddressType',
  trackSheetImportId: 'trackSheetImportId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  enteredBy: 'enteredBy'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  corporation_id: 'corporation_id',
  role_id: 'role_id',
  firstName: 'firstName',
  lastName: 'lastName',
  email: 'email',
  username: 'username',
  password: 'password',
  level: 'level',
  parent_id: 'parent_id',
  date_of_joining: 'date_of_joining',
  branch_id: 'branch_id',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.UserClientsScalarFieldEnum = {
  userId: 'userId',
  clientId: 'clientId'
};

exports.Prisma.UserTitleScalarFieldEnum = {
  id: 'id',
  level: 'level',
  title: 'title',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.VisibilityRuleScalarFieldEnum = {
  id: 'id',
  level: 'level',
  table_name: 'table_name',
  self: 'self',
  ancestors: 'ancestors',
  descendants: 'descendants',
  siblings: 'siblings'
};

exports.Prisma.WorkReportScalarFieldEnum = {
  id: 'id',
  date: 'date',
  user_id: 'user_id',
  client_id: 'client_id',
  carrier_id: 'carrier_id',
  work_type_id: 'work_type_id',
  work_status: 'work_status',
  task_type: 'task_type',
  planning_nummbers: 'planning_nummbers',
  expected_time: 'expected_time',
  actual_number: 'actual_number',
  start_time: 'start_time',
  finish_time: 'finish_time',
  time_spent: 'time_spent',
  pause: 'pause',
  resume: 'resume',
  category_id: 'category_id',
  notes: 'notes',
  switch_type: 'switch_type',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.WorkTypeScalarFieldEnum = {
  id: 'id',
  work_type: 'work_type',
  created_at: 'created_at',
  updated_at: 'updated_at',
  is_work_carrier_specific: 'is_work_carrier_specific',
  does_it_require_planning_number: 'does_it_require_planning_number',
  is_backlog_regular_required: 'is_backlog_regular_required',
  category_id: 'category_id'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};
exports.Worktype = exports.$Enums.Worktype = {
  trackSheets: 'trackSheets'
};

exports.WarningSeverity = exports.$Enums.WarningSeverity = {
  HIGH: 'HIGH',
  MEDIUM: 'MEDIUM',
  CRITICAL: 'CRITICAL'
};

exports.ImportStatus = exports.$Enums.ImportStatus = {
  inProgress: 'inProgress',
  success: 'success',
  failure: 'failure'
};

exports.FieldType = exports.$Enums.FieldType = {
  TEXT: 'TEXT',
  NUMBER: 'NUMBER',
  DATE: 'DATE',
  AUTO: 'AUTO'
};

exports.AutoOption = exports.$Enums.AutoOption = {
  DATE: 'DATE',
  USERNAME: 'USERNAME'
};

exports.DailyPlanningType = exports.$Enums.DailyPlanningType = {
  INVOICE_ENTRY_STATUS: 'INVOICE_ENTRY_STATUS',
  PF_STATUS: 'PF_STATUS',
  REVIEW_STATUS: 'REVIEW_STATUS',
  BATCH_ERROR_STATUS: 'BATCH_ERROR_STATUS',
  TWO_TEN_ERROR: 'TWO_TEN_ERROR',
  TWO_TEN_SUCCESS: 'TWO_TEN_SUCCESS',
  TWO_TEN_M_F: 'TWO_TEN_M_F',
  TWO_TEN_HOLD: 'TWO_TEN_HOLD',
  TWO_TEN_IMPORT_ADDITIONAL: 'TWO_TEN_IMPORT_ADDITIONAL',
  TWO_TEN_MANUAL_MATCH: 'TWO_TEN_MANUAL_MATCH',
  HOLD_STATUS: 'HOLD_STATUS',
  STATEMENT_TABLE: 'STATEMENT_TABLE',
  CORRECT: 'CORRECT',
  ENTRY: 'ENTRY'
};

exports.Bucket = exports.$Enums.Bucket = {
  ZERO_TO_SEVEN: 'ZERO_TO_SEVEN',
  EIGHT_TO_FIFTEEN: 'EIGHT_TO_FIFTEEN',
  SIXTEEN_TO_THIRTY: 'SIXTEEN_TO_THIRTY',
  THIRTY_ONE_TO_SIXTY: 'THIRTY_ONE_TO_SIXTY',
  SIXTY_ONE_TO_NINETY: 'SIXTY_ONE_TO_NINETY',
  NINETY_ONE_TO_HUNDRED_AND_TWENTY: 'NINETY_ONE_TO_HUNDRED_AND_TWENTY',
  HUNDRED_AND_TWENTY_PLUS: 'HUNDRED_AND_TWENTY_PLUS'
};

exports.PriorityStatus = exports.$Enums.PriorityStatus = {
  HIGH: 'HIGH',
  MEDIUM: 'MEDIUM',
  LOW: 'LOW'
};

exports.FreightTerm = exports.$Enums.FreightTerm = {
  PREPAID: 'PREPAID',
  COLLECT: 'COLLECT',
  THIRD_PARTY: 'THIRD_PARTY',
  LEGACY: 'LEGACY'
};

exports.AddressType = exports.$Enums.AddressType = {
  DC: 'DC',
  CV: 'CV',
  LEGACY: 'LEGACY'
};

exports.WorkStatus = exports.$Enums.WorkStatus = {
  STARTED: 'STARTED',
  PAUSED: 'PAUSED',
  RESUMED: 'RESUMED',
  FINISHED: 'FINISHED'
};

exports.TaskType = exports.$Enums.TaskType = {
  BACKLOG: 'BACKLOG',
  REGULAR: 'REGULAR'
};

exports.Switchtype = exports.$Enums.Switchtype = {
  INT: 'INT',
  EXT: 'EXT'
};

exports.Prisma.ModelName = {
  ClientCustomFieldArrangement: 'ClientCustomFieldArrangement',
  Comment: 'Comment',
  ManifestDetailsSchema: 'ManifestDetailsSchema',
  ManualMatchingMapping: 'ManualMatchingMapping',
  Pipeline: 'Pipeline',
  PipelineStage: 'PipelineStage',
  SystemGeneratedWarning: 'SystemGeneratedWarning',
  Tag: 'Tag',
  Ticket: 'Ticket',
  TicketStage: 'TicketStage',
  TrackSheetCustomFieldMapping: 'TrackSheetCustomFieldMapping',
  TrackSheetImport: 'TrackSheetImport',
  TrackSheetImportError: 'TrackSheetImportError',
  Associate: 'Associate',
  Branch: 'Branch',
  Carrier: 'Carrier',
  Category: 'Category',
  Client: 'Client',
  ClientCarrier: 'ClientCarrier',
  ClientFTPFilePathConfig: 'ClientFTPFilePathConfig',
  Corporation: 'Corporation',
  CustomField: 'CustomField',
  DailyPlanning: 'DailyPlanning',
  DailyPlanningByType: 'DailyPlanningByType',
  DailyPlanningDetails: 'DailyPlanningDetails',
  ImageFile: 'ImageFile',
  InvoiceFile: 'InvoiceFile',
  LegrandMapping: 'LegrandMapping',
  Roles: 'Roles',
  Permissions: 'Permissions',
  RolePermission: 'RolePermission',
  Session: 'Session',
  SuperAdmin: 'SuperAdmin',
  TicketStageChangeLog: 'TicketStageChangeLog',
  TrackSheets: 'TrackSheets',
  User: 'User',
  UserClients: 'UserClients',
  UserTitle: 'UserTitle',
  VisibilityRule: 'VisibilityRule',
  WorkReport: 'WorkReport',
  WorkType: 'WorkType'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)

import React, { useState, useRef, useEffect } from "react";
import { createPortal } from "react-dom";
import { IHeaderParams } from "ag-grid-community";
import { MdSearch, MdMoreVert, MdArrowDropUp, MdArrowDropDown } from "react-icons/md";

const PinnedHeader = (props: IHeaderParams) => {
  const [showOptions, setShowOptions] = useState(false);
  const [dropdownPos, setDropdownPos] = useState({ top: 0, left: 0 });
  const [sortDirection, setSortDirection] = useState<"asc" | "desc" | null>(
    props.column.getSort() as any
  );

  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const currentPinned = props.column.getPinned();

  // 👉 handle outside click
  const handleClickOutside = (event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node) &&
      buttonRef.current &&
      !buttonRef.current.contains(event.target as Node)
    ) {
      setShowOptions(false);
      document.removeEventListener("click", handleClickOutside);
    }
  };

  // 👉 update sortDirection when AG Grid changes sort externally
  useEffect(() => {
    const listener = () => {
      setSortDirection(props.column.getSort() as any);
    };
    props.api.addEventListener("sortChanged", listener);
    return () => props.api.removeEventListener("sortChanged", listener);
  }, [props.api, props.column]);

  const handlePinChange = (side: "left" | "right" | null) => {
    const columnId = props.column.getColId();
    props.api.setColumnsPinned([columnId], side);
    setShowOptions(false);
    document.removeEventListener("click", handleClickOutside);
  };

  const enableMenu = (event: React.MouseEvent) => {
    props.showColumnMenu(event.currentTarget as HTMLElement);
  };

  const toggleOptions = (e: React.MouseEvent) => {
    e.stopPropagation();
    const rect = buttonRef.current?.getBoundingClientRect();
    if (rect) {
      setDropdownPos({
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX,
      });
    }

    const next = !showOptions;
    setShowOptions(next);

    if (next) {
      document.addEventListener("click", handleClickOutside);
    } else {
      document.removeEventListener("click", handleClickOutside);
    }
  };

  const handleSortToggle = () => {
    if (!props.enableSorting || !props.progressSort) return;
    props.progressSort(); // toggles sorting
  };

  const getSortIcon = () => {
    if (sortDirection === "asc") return <MdArrowDropUp size={16} />;
    if (sortDirection === "desc") return <MdArrowDropDown size={16} />;
    return null;
  };

  const dropdown = showOptions
    ? createPortal(
        <div
          ref={dropdownRef}
          className="overflow-x-auto absolute z-[10000] bg-white border border-border rounded-md shadow-lg py-1 min-w-[120px] flex flex-col"
          style={{
            top: dropdownPos.top,
            left: dropdownPos.left,
          }}
        >
          {["left", "right", null].map((side: any) => {
            const label =
              side === "left"
                ? "Pin left"
                : side === "right"
                ? "Pin right"
                : "Unpin";
            const isSelected = currentPinned === side;

            return (
              <button
                key={label}
                onClick={() => handlePinChange(side)}
                className={`px-3 py-1.5 text-left text-sm flex items-center gap-1.5 cursor-pointer ${
                  isSelected ? "bg-primary/10" : "hover:bg-muted/50"
                }`}
              >
                <span
                  className={`w-2 h-2 rounded-full ${
                    isSelected
                      ? "bg-primary"
                      : "border border-border bg-transparent"
                  }`}
                />
                {label}
              </button>
            );
          })}
        </div>,
        document.body
      )
    : null;

  return (
    <>
      <div className="flex items-center gap-1.5 w-full min-w-0 h-full">
        <button
          onClick={handleSortToggle}
          className="overflow-hidden text-ellipsis whitespace-nowrap flex-1 text-muted-foreground text-sm text-left hover:underline focus:outline-none flex items-center gap-1"
          title="Click to sort"
        >
          {props.displayName}
          {getSortIcon()}
        </button>

        <button
          onClick={enableMenu}
          className="bg-muted border border-border rounded-md p-1 w-7 h-7 flex items-center justify-center hover:bg-muted-foreground/10 transition-colors"
          aria-label="Open filter"
        >
          <MdSearch size={16} className="text-muted-foreground" title="Search" />
        </button>

        <div className="relative flex items-center">
          <button
            ref={buttonRef}
            onClick={toggleOptions}
            className="bg-transparent border-none p-1 w-6 h-6 flex items-center justify-center rounded hover:bg-muted-foreground/10 transition-colors"
            aria-label="More options"
          >
            <MdMoreVert size={16} className="text-muted-foreground" />
          </button>
        </div>
      </div>
      {dropdown}
    </>
  );
};

export default PinnedHeader;

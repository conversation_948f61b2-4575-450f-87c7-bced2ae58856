import React, { useState } from 'react';
import { Alert<PERSON>ctagon, AlertTriangle, AlertCircle, ChevronDown, ChevronUp } from 'lucide-react';
import { Warning } from '../hooks/useWarningValidation';

interface WarningCollapsibleProps {
  warnings: { HIGH: Warning[]; MEDIUM: Warning[]; CRITICAL: Warning[] };
}

const WarningCollapsible: React.FC<WarningCollapsibleProps> = ({ warnings }) => {
  const [open, setOpen] = useState(true);
  const safeWarnings = warnings || { HIGH: [], MEDIUM: [], CRITICAL: [] };
  const highCount = safeWarnings.HIGH.length;
  const mediumCount = safeWarnings.MEDIUM.length;
  const criticalCount = safeWarnings.CRITICAL.length;
  const total = highCount + mediumCount + criticalCount;
  if (total === 0) return null;

  return (
    <div className="mt-2 w-full">
      <button
        type="button"
        className="flex items-center gap-2 text-xs font-semibold px-4 py-1 rounded-lg border border-gray-200 bg-gray-100 hover:bg-gray-200 w-full text-left"
        onClick={() => setOpen((v) => !v)}
        aria-expanded={open}
      >
        {criticalCount > 0 && <AlertCircle className="w-4 h-4 text-red-900" />}
        {highCount > 0 && <AlertOctagon className="w-4 h-4 text-red-600" />}
        {mediumCount > 0 && <AlertTriangle className="w-4 h-4 text-yellow-500" />}
        <span>
          {criticalCount > 0 && `${criticalCount} CRITICAL, `}
          {highCount > 0 && `${highCount} HIGH, `}
          {mediumCount > 0 && `${mediumCount} MEDIUM`}
          {total > 0 && ' warning' + (total > 1 ? 's' : '')}
        </span>
        {open ? <ChevronUp className="w-4 h-4 ml-1" /> : <ChevronDown className="w-4 h-4 ml-1" />}
        <span className="ml-1 text-blue-600 underline">Click to {open ? 'collapse' : 'expand'}</span>
      </button>
      {open && (
        <div className="mt-2 w-full px-4">
          {criticalCount > 0 && (
            <div>
              <div className="flex items-center gap-1 text-red-900 font-bold mb-1">
                <AlertCircle className="w-4 h-4" /> CRITICAL
              </div>
              <ul className="ml-5 list-disc text-xs">
                {safeWarnings.CRITICAL.map((w, i) => (
                  <li key={i}>{w.message}</li>
                ))}
              </ul>
            </div>
          )}
          {highCount > 0 && (
            <div>
              <div className="flex items-center gap-1 text-red-600 font-bold mb-1">
                <AlertOctagon className="w-4 h-4" /> HIGH
              </div>
              <ul className="ml-5 list-disc text-xs">
                {safeWarnings.HIGH.map((w, i) => (
                  <li key={i}>{w.message}</li>
                ))}
              </ul>
            </div>
          )}
          {mediumCount > 0 && (
            <div>
              <div className="flex items-center gap-1 text-yellow-600 font-bold mb-1">
                <AlertTriangle className="w-4 h-4" /> MEDIUM
              </div>
              <ul className="ml-5 list-disc text-xs">
                {safeWarnings.MEDIUM.map((w, i) => (
                  <li key={i}>{w.message}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default WarningCollapsible; 
import { legrandMapping_routes } from "@/lib/routePath";
import UpdateLegrandMapping from "./UpdateLegrandMapping";
import DeleteRow from "@/app/_component/DeleteRow";
import { PermissionWrapper } from "@/lib/permissionWrapper";

export const column = (permissions: string[]) => {
  const columns = [
    {
      field: "businessUnit",
      headerName: "Business Unit",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
    },
    {
      field: "legalName",
      headerName: "Legal Name",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
    },
    {
      field: "customeCode",
      headerName: "Custome Code",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
    },
    {
      field: "shippingBillingName",
      headerName: "Shipping Billing Name",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
    },
    {
      field: "shippingBillingAddress",
      headerName: "Shipping Billing Address",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
    },
    {
      field: "location",
      headerName: "Location",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
    },
    {
      field: "zipPostal",
      headerName: "Zip Postal",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
    },
    {
      field: "aliasCity",
      headerName: "Alias City",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
    },
    {
      field: "aliasShippingNames",
      headerName: "Alias Shipping Names",
      filter: "agTextColumnFilter",
      filterParams: {
        buttons: ["clear"],
      },
    },
    {
      field: "action",
      headerName: "Action",
      width: 150, 
      minWidth: 150, 
      maxWidth: 150, 
      cellRenderer: (params: any) => {
        const legrandMapping = params?.data;

        return (
          <div className="flex items-center">
            <PermissionWrapper
              permissions={permissions}
              requiredPermissions={["update-legrand-mappings"]}
            >
              <UpdateLegrandMapping data={legrandMapping} />
            </PermissionWrapper>

            <PermissionWrapper
              permissions={permissions}
              requiredPermissions={["delete-legrand-mappings"]}
            >
              <DeleteRow
                route={`${legrandMapping_routes.DELETE_LEGRAND_MAPPING}/${legrandMapping?.id}`}
              />
            </PermissionWrapper>
          </div>
        );
      },
      sortable: false,
      cellStyle: {
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        textAlign: "center", // Fallback
      },
    },
  ];
  return columns;
};

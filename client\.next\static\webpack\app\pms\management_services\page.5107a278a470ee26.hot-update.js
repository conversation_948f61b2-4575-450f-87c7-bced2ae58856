"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pms/management_services/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-spreadsheet.js":
/*!**********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/file-spreadsheet.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FileSpreadsheet; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.469.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst FileSpreadsheet = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"FileSpreadsheet\", [\n    [\n        \"path\",\n        {\n            d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\",\n            key: \"1rqfz7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n            key: \"tnqrlb\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 13h2\",\n            key: \"yr2amv\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14 13h2\",\n            key: \"un5t4a\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 17h2\",\n            key: \"2yhykz\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14 17h2\",\n            key: \"10kma7\"\n        }\n    ]\n]);\n //# sourceMappingURL=file-spreadsheet.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-spreadsheet.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/pms/management_services/page.tsx":
/*!**********************************************!*\
  !*** ./app/pms/management_services/page.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FaBriefcase_FaBuilding_FaGripHorizontal_FaPenFancy_FaStream_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=FaBriefcase,FaBuilding,FaGripHorizontal,FaPenFancy,FaStream!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_RiFileAddLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=RiFileAddLine!=!react-icons/ri */ \"(app-pages-browser)/./node_modules/react-icons/ri/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_TbTicket_react_icons_tb__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=TbTicket!=!react-icons/tb */ \"(app-pages-browser)/./node_modules/react-icons/tb/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaFileArrowDown_react_icons_fa6__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=FaFileArrowDown!=!react-icons/fa6 */ \"(app-pages-browser)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,FileSpreadsheetIcon,FileText,FileUpIcon,Grid3X3,Search,Shield,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,FileSpreadsheetIcon,FileText,FileUpIcon,Grid3X3,Search,Shield,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,FileSpreadsheetIcon,FileText,FileUpIcon,Grid3X3,Search,Shield,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,FileSpreadsheetIcon,FileText,FileUpIcon,Grid3X3,Search,Shield,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,FileSpreadsheetIcon,FileText,FileUpIcon,Grid3X3,Search,Shield,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,FileSpreadsheetIcon,FileText,FileUpIcon,Grid3X3,Search,Shield,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,FileSpreadsheetIcon,FileText,FileUpIcon,Grid3X3,Search,Shield,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,FileSpreadsheetIcon,FileText,FileUpIcon,Grid3X3,Search,Shield,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,FileSpreadsheetIcon,FileText,FileUpIcon,Grid3X3,Search,Shield,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-spreadsheet.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,FileSpreadsheetIcon,FileText,FileUpIcon,Grid3X3,Search,Shield,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,FileSpreadsheetIcon,FileText,FileUpIcon,Grid3X3,Search,Shield,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,FileSpreadsheetIcon,FileText,FileUpIcon,Grid3X3,Search,Shield,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _manage_tickets_TicketContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../manage_tickets/TicketContext */ \"(app-pages-browser)/./app/pms/manage_tickets/TicketContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// --- Real Routes and Permission Logic ---\nconst Routes = [\n    // User & Roles Management\n    {\n        label: \"Manage User\",\n        path: \"/pms/manage_employee\",\n        permission: {\n            module: \"USER MANAGEMENT\"\n        },\n        icon: _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        category: \"User & Roles Management\"\n    },\n    {\n        label: \"Manage Roles\",\n        path: \"/pms/manage-roles\",\n        permission: {\n            module: \"ROLE MANAGEMENT\"\n        },\n        icon: _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        category: \"User & Roles Management\"\n    },\n    {\n        label: \"Manage Associate\",\n        path: \"/pms/manage_associate\",\n        permission: {\n            module: \"ASSOCIATE MANAGEMENT\"\n        },\n        icon: _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        category: \"User & Roles Management\"\n    },\n    // Organization Management\n    {\n        label: \"Manage Branch\",\n        path: \"/pms/manage_branch\",\n        permission: {\n            module: \"BRANCH MANAGEMENT\"\n        },\n        icon: _barrel_optimize_names_FaBriefcase_FaBuilding_FaGripHorizontal_FaPenFancy_FaStream_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaBuilding,\n        category: \"Organization Management\"\n    },\n    {\n        label: \"Manage Client\",\n        path: \"/pms/manage_client\",\n        permission: {\n            module: \"CLIENT MANAGEMENT\",\n            action: \"create-client\"\n        },\n        icon: _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        category: \"Organization Management\"\n    },\n    {\n        label: \"Manage Carrier\",\n        path: \"/pms/manage_carrier\",\n        permission: {\n            module: \"CARRIER MANAGEMENT\"\n        },\n        icon: _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        category: \"Organization Management\"\n    },\n    {\n        label: \"Manage Category\",\n        path: \"/pms/manage_category\",\n        permission: {\n            module: \"CATEGORY MANAGEMENT\"\n        },\n        icon: _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        category: \"Organization Management\"\n    },\n    // Customizations\n    {\n        label: \"Add/Update Custom Fields\",\n        path: \"/pms/addupdate_custom_fields\",\n        permission: {\n            module: \"CUSTOM FIELD MANAGEMENT\"\n        },\n        icon: _barrel_optimize_names_FaBriefcase_FaBuilding_FaGripHorizontal_FaPenFancy_FaStream_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaPenFancy,\n        category: \"Customizations\"\n    },\n    {\n        label: \"Arrange Custom Fields\",\n        path: \"/pms/arrange_custom_fields\",\n        permission: {\n            module: \"CUSTOM FIELD ARRANGEMENT\"\n        },\n        icon: _barrel_optimize_names_FaBriefcase_FaBuilding_FaGripHorizontal_FaPenFancy_FaStream_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaGripHorizontal,\n        category: \"Customizations\"\n    },\n    {\n        label: \"Manage File Path\",\n        path: \"/pms/manage_file_path\",\n        permission: {\n            module: \"FILE PATH MANAGEMENT\"\n        },\n        icon: _barrel_optimize_names_FaFileArrowDown_react_icons_fa6__WEBPACK_IMPORTED_MODULE_13__.FaFileArrowDown,\n        category: \"Customizations\"\n    },\n    // Operations\n    {\n        label: \"Manage Work Type\",\n        path: \"/pms/manage_work_type\",\n        permission: {\n            module: \"WORKTYPE MANAGEMENT\",\n            action: \"update-work\"\n        },\n        icon: _barrel_optimize_names_FaBriefcase_FaBuilding_FaGripHorizontal_FaPenFancy_FaStream_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaBriefcase,\n        category: \"Operations\"\n    },\n    {\n        label: \"Manage Work Report\",\n        path: \"/pms/manage_work_report\",\n        permission: {\n            module: \"WORK REPORT\"\n        },\n        icon: _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        category: \"Operations\"\n    },\n    {\n        label: \"Manage Report\",\n        path: \"/pms/customize_report\",\n        permission: {\n            module: \"CUSTOMIZE REPORT\"\n        },\n        icon: _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        category: \"Operations\"\n    },\n    {\n        label: \"TrackSheets\",\n        path: \"/pms/track_sheets\",\n        permission: {\n            module: \"TRACKSHEET REPORT\"\n        },\n        icon: _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        category: \"Operations\"\n    },\n    {\n        label: \"Manage Imported Files\",\n        path: \"/user/trackSheets/imported-files\",\n        permission: {\n            module: \"IMPORTED FILES MANAGEMENT\"\n        },\n        icon: _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n        category: \"Operations\"\n    },\n    {\n        label: \"Manage Pipelines\",\n        path: \"/pms/manage_pipelines\",\n        permission: {\n            module: \"PIPELINE MANAGEMENT\"\n        },\n        icon: _barrel_optimize_names_FaBriefcase_FaBuilding_FaGripHorizontal_FaPenFancy_FaStream_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaStream,\n        category: \"Customizations\"\n    },\n    {\n        label: \"Manage Tickets\",\n        path: \"/pms/manage_tickets\",\n        permission: {\n            module: \"TICKET MANAGEMENT\"\n        },\n        icon: _barrel_optimize_names_TbTicket_react_icons_tb__WEBPACK_IMPORTED_MODULE_18__.TbTicket,\n        category: \"Operations\"\n    },\n    {\n        label: \"Manage Legrand Mappings\",\n        path: \"/pms/manage_legrand_mappings\",\n        permission: {\n            module: \"LEGRAND MAPPING\"\n        },\n        icon: _barrel_optimize_names_RiFileAddLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_19__.RiFileAddLine,\n        category: \"Operations\"\n    },\n    {\n        label: \"Manage Invoice Files\",\n        path: \"/pms/manage_invoice_files\",\n        permission: {\n            module: \"INVOICE FILE MANAGEMENT\"\n        },\n        icon: _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n        category: \"Operations\"\n    }\n];\nconst categoryOrder = [\n    \"User & Roles Management\",\n    \"Organization Management\",\n    \"Customizations\",\n    \"Operations\"\n];\nconst categoryIcons = {\n    \"User & Roles Management\": _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    \"Organization Management\": _barrel_optimize_names_FaBriefcase_FaBuilding_FaGripHorizontal_FaPenFancy_FaStream_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaBuilding,\n    Customizations: _barrel_optimize_names_FaBriefcase_FaBuilding_FaGripHorizontal_FaPenFancy_FaStream_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaPenFancy,\n    Operations: _barrel_optimize_names_FaBriefcase_FaBuilding_FaGripHorizontal_FaPenFancy_FaStream_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaStream\n};\nconst categoryColors = {\n    \"User & Roles Management\": \"bg-blue-500\",\n    \"Organization Management\": \"bg-orange-500\",\n    Customizations: \"bg-purple-500\",\n    Operations: \"bg-green-500\"\n};\n// Add helper to get recent service paths from localStorage\nfunction getRecentServicePaths() {\n    try {\n        return JSON.parse(localStorage.getItem(\"recentServices\") || \"[]\");\n    } catch (e) {\n        return [];\n    }\n}\nconst ManagementServicesPage = ()=>{\n    _s();\n    const { currentUser } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_manage_tickets_TicketContext__WEBPACK_IMPORTED_MODULE_7__.TicketContext);\n    const userKey = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.id) || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.username) || \"guest\";\n    const RECENTS_KEY = \"recentServices_\".concat(userKey);\n    function getRecentServicePaths() {\n        try {\n            return JSON.parse(localStorage.getItem(RECENTS_KEY) || \"[]\");\n        } catch (e) {\n            return [];\n        }\n    }\n    function setRecentServicePaths(recents) {\n        localStorage.setItem(RECENTS_KEY, JSON.stringify(recents));\n    }\n    const FAVORITES_KEY = \"favoriteServices_\".concat(userKey);\n    function getFavoriteServicePaths() {\n        try {\n            return JSON.parse(localStorage.getItem(FAVORITES_KEY) || \"[]\");\n        } catch (e) {\n            return [];\n        }\n    }\n    function setFavoriteServicePaths(favorites) {\n        localStorage.setItem(FAVORITES_KEY, JSON.stringify(favorites));\n    }\n    // Dummy state to force re-render on favorite toggle\n    const [dummyState, setDummyState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    react__WEBPACK_IMPORTED_MODULE_1___default().useLayoutEffect(()=>{\n        setIsClient(true);\n        const storedPermissions = sessionStorage.getItem(\"permissions\");\n        if (storedPermissions) {\n            try {\n                const parsedPermissions = JSON.parse(storedPermissions);\n                const validPermissions = parsedPermissions.filter((perm)=>perm !== null);\n                setPermissions(validPermissions.length > 0 ? validPermissions : [\n                    \"admin\"\n                ]);\n            } catch (error) {\n                setPermissions([\n                    \"admin\"\n                ]);\n            }\n        } else {\n            setPermissions([\n                \"admin\"\n            ]);\n        }\n    }, []);\n    if (!isClient) return null;\n    const hasAllowAllPermission = permissions.includes(\"allow_all\");\n    const filteredRoutes = hasAllowAllPermission ? Routes : permissions.includes(\"admin\") ? Routes : Routes.filter((route)=>permissions.some((permission)=>typeof permission === \"string\" && permission === route.permission.module));\n    // Group routes by category\n    const groupedRoutes = filteredRoutes.reduce((acc, route)=>{\n        if (!acc[route.category]) acc[route.category] = [];\n        acc[route.category].push(route);\n        return acc;\n    }, {});\n    // Filtered grouped routes by search\n    const filteredGroupedRoutes = Object.fromEntries(Object.entries(groupedRoutes).map((param)=>{\n        let [category, routes] = param;\n        return [\n            category,\n            routes.filter((route)=>route.label.toLowerCase().includes(searchQuery.toLowerCase()))\n        ];\n    }));\n    // All services for tabs\n    const allServices = filteredRoutes.map((route)=>({\n            ...route,\n            category: route.category\n        }));\n    // Real recently used services\n    const recentServices = getRecentServicePaths().map((path)=>allServices.find((s)=>s.path === path)).filter(Boolean);\n    // Real favorite services\n    const favoriteServicePaths = getFavoriteServicePaths();\n    const favoriteServices = favoriteServicePaths.map((path)=>allServices.find((s)=>s.path === path)).filter(Boolean);\n    // Handler to update recent services and navigate\n    const handleServiceClick = (service)=>{\n        let recent = [];\n        try {\n            recent = JSON.parse(localStorage.getItem(RECENTS_KEY) || \"[]\");\n        } catch (e) {}\n        recent = recent.filter((id)=>id !== service.path);\n        recent.unshift(service.path);\n        if (recent.length > 6) recent = recent.slice(0, 6);\n        localStorage.setItem(RECENTS_KEY, JSON.stringify(recent));\n        window.location.href = service.path;\n    };\n    // Handler to toggle favorite status\n    const handleFavoriteClick = (service, e)=>{\n        e.stopPropagation();\n        let favorites = getFavoriteServicePaths();\n        if (favorites.includes(service.path)) {\n            favorites = favorites.filter((id)=>id !== service.path);\n        } else {\n            favorites.unshift(service.path);\n            if (favorites.length > 12) favorites = favorites.slice(0, 12);\n        }\n        setFavoriteServicePaths(favorites);\n        // Force re-render by using a dummy state\n        setDummyState((s)=>!s);\n    };\n    // Check if any category has visible routes after filtering\n    const hasVisibleRoutes = Object.values(filteredGroupedRoutes).some((routes)=>routes.length > 0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50/50 p-4 md:p-6 lg:p-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-auto max-w-7xl space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold tracking-tight text-gray-900\",\n                            children: \"Management Services\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Centralized command center for all administrative modules and operations\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                    lineNumber: 366,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative max-w-2xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                            type: \"text\",\n                            placeholder: \"Search services, features, or modules...\",\n                            value: searchQuery,\n                            onChange: (e)=>setSearchQuery(e.target.value),\n                            className: \"pl-12 pr-4 py-3 text-lg border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-0 shadow-sm\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                    lineNumber: 377,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                            className: \"grid w-full grid-cols-4 lg:w-fit lg:grid-cols-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"all\",\n                                    children: \"All Services\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"recent\",\n                                    children: \"Recent\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"favorites\",\n                                    children: \"Favorites\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"categories\",\n                                    children: \"Categories\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"all\",\n                            className: \"space-y-8\",\n                            children: categoryOrder.map((categoryName)=>{\n                                var _filteredGroupedRoutes_categoryName;\n                                return ((_filteredGroupedRoutes_categoryName = filteredGroupedRoutes[categoryName]) === null || _filteredGroupedRoutes_categoryName === void 0 ? void 0 : _filteredGroupedRoutes_categoryName.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    id: categoryName.replace(/\\s+/g, \"-\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-lg \".concat(categoryColors[categoryName], \" text-white\"),\n                                                    children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(categoryIcons[categoryName] || _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                    children: categoryName\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"secondary\",\n                                                    children: filteredGroupedRoutes[categoryName].length\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4\",\n                                            children: filteredGroupedRoutes[categoryName].map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                    className: \"group hover:shadow-xl hover:-translate-y-1 transition-all duration-300 cursor-pointer border-0 shadow-md hover:shadow-blue-100\",\n                                                    onClick: ()=>handleServiceClick(service),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                            className: \"pb-3 relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col items-center justify-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"p-2 rounded-lg bg-gray-100 group-hover:bg-blue-100 transition-colors mb-2\",\n                                                                            children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(service.icon || _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                className: \"h-5 w-5 text-gray-600 group-hover:text-blue-600\"\n                                                                            })\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                            lineNumber: 433,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                                            className: \"text-lg group-hover:text-blue-600 transition-colors text-center\",\n                                                                            children: service.label\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                            lineNumber: 439,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    className: \"opacity-0 group-hover:opacity-100 transition-opacity absolute top-2 right-2\",\n                                                                    onClick: (e)=>handleFavoriteClick(service, e),\n                                                                    children: favoriteServicePaths.includes(service.path) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-yellow-500 fill-current\",\n                                                                        fill: \"currentColor\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                        lineNumber: 450,\n                                                                        columnNumber: 31\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                        lineNumber: 455,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                    lineNumber: 443,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                            className: \"pt-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, service.path, true, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, categoryName, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 17\n                                }, undefined) : null;\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"recent\",\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-semibold text-gray-900\",\n                                            children: \"Recently Used\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Clear History\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                    children: recentServices.map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"group hover:shadow-lg transition-all duration-200 cursor-pointer\",\n                                            onClick: ()=>handleServiceClick(service),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                className: \"pb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center justify-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded-lg bg-gray-100 group-hover:bg-blue-100 transition-colors mb-2\",\n                                                            children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(service.icon || _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-600 group-hover:text-blue-600\"\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                            className: \"text-base group-hover:text-blue-600 transition-colors text-center\",\n                                                            children: service.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 text-center\",\n                                                            children: service.category\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, service.path, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"favorites\",\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-semibold text-gray-900\",\n                                            children: \"Favorite Services\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: \"secondary\",\n                                            children: [\n                                                favoriteServices.length,\n                                                \" favorites\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                    children: favoriteServices.map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"group hover:shadow-lg transition-all duration-200 cursor-pointer border-yellow-200\",\n                                            onClick: ()=>handleServiceClick(service),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                className: \"pb-3 relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center justify-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 rounded-lg bg-yellow-100 group-hover:bg-yellow-200 transition-colors mb-2\",\n                                                                children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(service.icon || _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-yellow-600\"\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                lineNumber: 530,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                                className: \"text-base group-hover:text-yellow-600 transition-colors text-center\",\n                                                                children: service.label\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                lineNumber: 535,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 text-center\",\n                                                                children: service.category\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                lineNumber: 538,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        className: \"opacity-0 group-hover:opacity-100 transition-opacity absolute top-2 right-2\",\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            handleFavoriteClick(service, e);\n                                                        },\n                                                        children: favoriteServicePaths.includes(service.path) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-4 w-4 text-yellow-500 fill-current\",\n                                                            fill: \"currentColor\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 25\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, service.path, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                            lineNumber: 512,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"categories\",\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold text-gray-900\",\n                                    children: \"Service Categories\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: categoryOrder.map((categoryName)=>{\n                                        var _groupedRoutes_categoryName;\n                                        return ((_groupedRoutes_categoryName = groupedRoutes[categoryName]) === null || _groupedRoutes_categoryName === void 0 ? void 0 : _groupedRoutes_categoryName.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"group hover:shadow-lg transition-all duration-200 cursor-pointer\",\n                                            onClick: ()=>{\n                                                setActiveTab(\"all\");\n                                                setTimeout(()=>{\n                                                    const el = document.getElementById(categoryName.replace(/\\s+/g, \"-\"));\n                                                    if (el) {\n                                                        el.scrollIntoView({\n                                                            behavior: \"smooth\",\n                                                            block: \"start\"\n                                                        });\n                                                    }\n                                                }, 50); // slight delay to ensure tab content is rendered\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 rounded-xl \".concat(categoryColors[categoryName], \" text-white group-hover:scale-110 transition-transform\"),\n                                                                children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(categoryIcons[categoryName] || _barrel_optimize_names_Activity_BarChart3_Clock_FileSpreadsheetIcon_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-6 w-6\"\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                lineNumber: 594,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                                        className: \"text-lg group-hover:text-blue-600 transition-colors\",\n                                                                        children: categoryName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                        lineNumber: 603,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: [\n                                                                            groupedRoutes[categoryName].length,\n                                                                            \" services\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                        lineNumber: 606,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                lineNumber: 602,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                        lineNumber: 593,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            groupedRoutes[categoryName].slice(0, 3).map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 text-sm text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-1.5 h-1.5 rounded-full bg-gray-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                            lineNumber: 621,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        service.label\n                                                                    ]\n                                                                }, service.path, true, {\n                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                    lineNumber: 617,\n                                                                    columnNumber: 29\n                                                                }, undefined)),\n                                                            groupedRoutes[categoryName].length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"+\",\n                                                                    groupedRoutes[categoryName].length - 3,\n                                                                    \" more services\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                lineNumber: 626,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                        lineNumber: 613,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                    lineNumber: 612,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, categoryName, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 19\n                                        }, undefined) : null;\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                            lineNumber: 567,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                    lineNumber: 387,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n            lineNumber: 364,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n        lineNumber: 363,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ManagementServicesPage, \"5Yxm9tWB8B8/wl6mF3HJzTGcXbc=\");\n_c = ManagementServicesPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ManagementServicesPage);\nvar _c;\n$RefreshReg$(_c, \"ManagementServicesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/pms/management_services/page.tsx\n"));

/***/ })

});
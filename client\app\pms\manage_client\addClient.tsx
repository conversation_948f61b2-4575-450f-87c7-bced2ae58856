"use client";
import FormInput from "@/app/_component/FormInput";
import SelectComp from "@/app/_component/SelectComp";
import SubmitBtn from "@/app/_component/SubmitBtn";
import FormTextarea from "@/app/_component/TextArea";
import { Form } from "@/components/ui/form";
import { SelectItem } from "@/components/ui/select";
import { formSubmit, getAllData } from "@/lib/helpers";
import { client_routes, location_api, search_routes } from "@/lib/routePath";
import useDynamicForm from "@/lib/useDynamicForm";
import { createClientSchema } from "@/lib/zodSchema";
import React, { useState } from "react";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FaPlus } from "react-icons/fa";
import DialogHeading from "@/app/_component/DialogHeading";
import { useRouter } from "next/navigation";
import TriggerButton from "@/app/_component/TriggerButton";
import { SheetDemoClient } from "./ImportClient";
import * as XLSX from "xlsx";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import * as ExcelJS from "exceljs";

function AddClient({ data, allBranch, allUser, params, allAssociate }: any) {
  //  (allUser);
  //  (allBranch);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const exportParams = new URLSearchParams(params);

  exportParams.delete("pageSize");
  exportParams.delete("page");

  const { form } = useDynamicForm(createClientSchema, {
    associate: "",
    client_name: "",
    // ownership: "",
    ownership: "",
    branch: "",
  });
  // const branch_name = form.watch("branch_name");

  // useEffect(() => {
  //   if (branch_name) {
  //     fetchStates(branch_name);
  //   }
  // }, [branch_name, fetchStates]);
  async function onSubmit(values: any) {
    console.log(values, "values");
    try {
      const formData = {
        associate: values.associate,
        client_name: values.client_name.toUpperCase().trim(),
        ownership: values.ownership,
        branch: values.branch,
      };
      console.log(formData, "formData");

      const data = await formSubmit(
        client_routes.CREATE_CLIENT,
        "POST",
        formData
      );
      console.log(data, "data");

      if (data.success) {
        toast.success(data.message);

        router.refresh();
        form.reset();
        setIsDialogOpen(false);
        form.reset();
      } else {
        toast.error(data.error || "An error occurred while adding the client.");
      }
    } catch (error) {
      toast.error("An error occurred while adding the client.");
      console.error(error);
    }
  }

  const handleExportClient = async () => {
    setIsLoading(true);

    // try {
    //   const response = await fetch(
    //     `${client_routes.EXCEL_CLIENT}?${exportParams.toString()}`,
    //     {
    //       method: "GET",
    //     }
    //   );
    //   if (response.ok) {
    //     const blob = await response.blob();
    //     if (!blob || blob.size === 0) {
    //       throw new Error("Error: Received an empty or invalid file.");
    //     }
    //     const link = document.createElement("a");
    //     link.href = URL.createObjectURL(blob);
    //     link.download = "client.xlsx";
    //     link.click();
    //     toast.success("Clients Exported Successfully");
    //   } else {
    //     throw new Error(`Unexpected response: ${response.statusText}`);
    //   }
    // } catch (error) {
    //   console.error("Error exporting clients:", error);
    //   toast.error(`Error exporting clients: ${error.message}`);
    // }
    try {
      const apiUrl = `${
        search_routes.GET_SEARCH
      }/client?${exportParams.toString()}`;
      const allClient = await getAllData(apiUrl);

      if (
        allClient &&
        allClient.data &&
        Array.isArray(allClient.data) &&
        allClient.data.length > 0
      ) {
        const clientExcelData = allClient.data;
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet("Client");

        // Add headers
        const headers = ["Client Name", "Associate", "Ownership", "Branch"];
        worksheet.addRow(headers);

        clientExcelData.forEach((item) => {
          const formattedRow = [
            item.client_name || "N/A",
            item.associate?.name || "N/A",
            item.ownership?.username || "N/A",
            item.branch?.branch_name || "N/A",
          ];
          worksheet.addRow(formattedRow);
        });

        const fileBuffer = await workbook.xlsx.writeBuffer();

        const blob = new Blob([fileBuffer], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });

        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = "Client.xlsx";
        link.click();
        toast.success("Clients Exported Successfully");
      } else {
        throw new Error("Error: No valid data found for export.");
      }
    } catch (error) {
      console.error("Error exporting client:", error);
      toast.error(`Error exporting client: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <Button onClick={handleExportClient} className="mr-2">
          Export
        </Button>
        <SheetDemoClient />
        <DialogTrigger>
          <TriggerButton type="add" text="Client" />
        </DialogTrigger>
        <DialogContent className="md:min-w-[50rem] min-w-[40rem]">
          <DialogHeading
            title="Add Client"
            description="Please Enter Client Details"
          />

          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(
                (values) => onSubmit(values),
                (errors) => console.error("Validation Errors:", errors)
              )}
            >
              <div className="grid grid-cols-2 gap-5 items-center ">
                <SelectComp
                  form={form}
                  label="Associate"
                  name="associate"
                  placeholder="Select Associate"
                  isRequired
                >
                  {allAssociate.map((associates: any) => (
                    <SelectItem
                      value={associates.id?.toString()}
                      key={associates.id}
                    >
                      {associates.name}
                    </SelectItem>
                  ))}
                </SelectComp>

                <FormInput
                  form={form}
                  label="Client Name"
                  name="client_name"
                  placeholder="Enter Client Name"
                  type="text"
                  isRequired
                />
              </div>

              <div className="grid grid-cols-2 gap-5 items-center">
                <SelectComp
                  form={form}
                  label="Ownership"
                  name="ownership"
                  placeholder="Select Ownership"
                  isRequired
                >
                  {allUser
                    .filter((user: any) => user.level === 4 || user.level === 3)
                    .map((user: any) => (
                      <SelectItem value={user.id?.toString()} key={user.id}>
                        {user.username}
                      </SelectItem>
                    ))}
                </SelectComp>

                <SelectComp
                  form={form}
                  label="Branch"
                  name="branch"
                  placeholder="Select Branch"
                  isRequired
                >
                  {allBranch.map((branch: any) => (
                    <SelectItem value={branch.id.toString()} key={branch.id}>
                      {branch.branch_name}
                    </SelectItem>
                  ))}
                </SelectComp>
              </div>

              <SubmitBtn
                className="w-full bg-primary text-secondary hover:bg-primary/90"
                text="Submit"
              />
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}

export default AddClient;

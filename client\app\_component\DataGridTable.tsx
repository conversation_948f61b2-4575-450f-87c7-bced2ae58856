"use client";
import React, { useState, useEffect, useRef, useContext, useMemo } from "react";
import { FaFilter, FaSearch } from "react-icons/fa";
import { useRouter, useSearchParams, usePathname } from "next/navigation";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import Pagination from "./Pagination";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";
import { AllCommunityModule, ModuleRegistry } from "ag-grid-community";
import { WorkReportContext } from "../pms/manage_work_report/WorkReportContext";
import { TrackerContext } from "../user/tracker/TrackerContext";
import { cn } from "@/lib/utils";
import { CustomInput } from "@/components/ui/customInput";

// Register AG Grid modules
ModuleRegistry.registerModules([AllCommunityModule]);

interface DataGridTableProps {
  columns: any[];
  data: any[];
  isLoading?: boolean;
  showColDropDowns?: boolean;
  filter?: boolean;
  filter1PlaceHolder?: string;
  filter2?: boolean;
  filter3?: boolean;
  filter3view?: JSX.Element;
  total?: boolean;
  totalview?: JSX.Element;
  filter_column?: string;
  filter_column2?: string;
  filter_column3?: string;
  filter_column4?: string;
  overflow?: boolean;
  totalPages?: number;
  showPageEntries?: boolean;
  className?: string;
  showSearchColumn?: boolean;
  pageSize?: number;
  isTimerRunning?: any;
  setIsTimerRunning?: any;
  onFilteredDataChange?: (filteredData: any[]) => void;
}

const DataGridTable = ({
  columns,
  data,
  isLoading,
  showColDropDowns,
  filter,
  filter2,
  filter3 = false,
  filter3view,
  total,
  totalview,
  filter_column,
  filter_column2,
  filter_column3,
  filter_column4,
  totalPages,
  showPageEntries,
  className,
  filter1PlaceHolder,
  showSearchColumn = true,
  pageSize,
  isTimerRunning,
  setIsTimerRunning,
  onFilteredDataChange,
}: DataGridTableProps) => {
  const [page, setPage] = useState<number>(pageSize || 50);
  const [selectedColumns, setSelectedColumns] = useState<string[]>([]);
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams);
  const pathname = usePathname();
  const { replace } = useRouter();

  const currentPage = Number(searchParams.get("page")) || 1;
  const currentPageSize =
    Number(searchParams.get("pageSize")) || pageSize || 50;

  const [pagination, setPagination] = useState<any>({
    pageIndex: 0,
    pageSize: page,
  });

  const { setFromDate, fromDate, toDate, setToDate } =
    useContext(WorkReportContext);
  const { setFDate, fDate, tDate, setTDate } = useContext(TrackerContext);

  const [columnState, setColumnState] = useState<any[]>([]);
  const gridRef = useRef<AgGridReact>(null);

  const [columnData, setColumnData] = useState("");
  const [columnVisibility, setColumnVisibility] = useState<any>({});

  const [inputValues, setInputValues] = useState<{ [key: string]: string }>({});

  const [searchTerms, setSearchTerms] = useState<{ [key: string]: string[] }>(
    {}
  );

  // Prepare data with stable serial numbers
  const processedData = useMemo(() => {
    return data?.map((item, index) => ({
      ...item,
      stableId: (currentPage - 1) * currentPageSize + index + 1,
    }));
  }, [data, currentPage, currentPageSize]);

  // Prepare columns with serial number
  const columnsWithSerialNumber = useMemo(() => {
    return [
      {
        headerName: "Sr. No.",
        field: "stableId",
        sortable: true,
        width: 80,
        minWidth: 80, // Prevent shrinking
        maxWidth: 80, // Prevent expanding
        cellStyle: {
          textAlign: "center",
        },
        pinned: "left",

        comparator: (valueA: number, valueB: number) => valueA - valueB,
      },
      ...columns.filter((col) => col.field !== "sr_no"),
    ];
  }, [columns]);

  // Initialize all columns as visible
  useEffect(() => {
    const initialVisibility: any = {};
    columnsWithSerialNumber.forEach((col) => {
      initialVisibility[col.field] = true;
    });
    setColumnVisibility(initialVisibility);
  }, [columnsWithSerialNumber]);

  useEffect(() => {
    if (columnData !== "date") {
      setFromDate(""), setToDate("");
      setFDate(""), setTDate("");
    }
  }, [columnData]);

  // Show or hide overlays based on loading and data state
  useMemo(() => {
    if (gridRef.current && gridRef.current.api) {
      if (isLoading) {
        gridRef.current.api.showLoadingOverlay();
      } else if (!processedData || processedData.length === 0) {
        gridRef.current.api.showNoRowsOverlay();
      } else {
        gridRef.current.api.hideOverlay();
      }
    }
  }, [isLoading, processedData]);

  // Handle page change
  const handlePageChange = (e: any) => {
    const newPageSize = parseInt(e.target.value);
    setPage(newPageSize);
    if (totalPages) {
      params.set("pageSize", newPageSize?.toString());
      replace(`${pathname}?${params.toString()}`);
    }
    setPagination((prevState) => ({
      ...prevState,
      pageSize: newPageSize,
    }));
  };

  // Handle column selection
  const handleColumnSelection = (columnKey: string, columnHeader: string) => {
    setSelectedColumns((prevSelectedColumns) => {
      let updatedColumns;

      if (prevSelectedColumns.includes(columnKey)) {
        updatedColumns = prevSelectedColumns.filter((col) => col !== columnKey);

        const updatedParams = new URLSearchParams(searchParams);

        if (columnHeader === "Date") {
          updatedParams.delete("fDate");
          updatedParams.delete("tDate");
        } else {
          updatedParams.delete(columnHeader);
        }

        replace(`${pathname}?${updatedParams.toString()}`);
      } else {
        updatedColumns = [...prevSelectedColumns, columnKey];

        const updatedParams = new URLSearchParams(searchParams);
        updatedParams.set(columnHeader, "");
        replace(`${pathname}?${updatedParams.toString()}`);
      }

      return updatedColumns;
    });
  };

  // Search functionality
  const handleSearchChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    columnName: string
  ) => {
    const value = event.target.value.trim();

    if (value) {
      const updatedParams = new URLSearchParams(searchParams);
      updatedParams.set(columnName, value);
      updatedParams.set("page", "1");
      replace(`${pathname}?${updatedParams.toString()}`);
    } else {
      const updatedParams = new URLSearchParams(searchParams);
      updatedParams.delete(columnName);
      replace(`${pathname}?${updatedParams.toString()}`);
    }
  };

  const handleKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>,
    columnName: string
  ) => {
    const currentInput = inputValues[columnName]?.trim();

    if (e.key === "Enter") {
      e.preventDefault();
      if (currentInput && !searchTerms[columnName]?.includes(currentInput)) {
        const updated = [...(searchTerms[columnName] || []), currentInput];
        updateSearchParams(columnName, updated);
        setSearchTerms({ ...searchTerms, [columnName]: updated });
        setInputValues({ ...inputValues, [columnName]: "" });
      }
    }

    if (
      e.key === "Backspace" &&
      !currentInput &&
      searchTerms[columnName]?.length > 0
    ) {
      const updated = [...searchTerms[columnName]];
      updated.pop();
      updateSearchParams(columnName, updated);
      setSearchTerms({ ...searchTerms, [columnName]: updated });
    }
  };

  const handleRemoveTerm = (columnName: string, term: string) => {
    const updated = (searchTerms[columnName] || []).filter((t) => t !== term);
    updateSearchParams(columnName, updated);
    setSearchTerms({ ...searchTerms, [columnName]: updated });
  };

  const updateSearchParams = (columnName: string, terms: string[]) => {
    const updatedParams = new URLSearchParams(searchParams);
    if (terms.length > 0) {
      updatedParams.set(columnName, terms.join(","));
      updatedParams.set("page", "1");
    } else {
      updatedParams.delete(columnName);
    }
    replace(`${pathname}?${updatedParams.toString()}`);
  };

  // Filter columns based on selected ones
  const filterColumns = selectedColumns.length
    ? selectedColumns.map((columnKey) =>
        columnsWithSerialNumber.find((col) => col.field === columnKey)
      )
    : [];

  const toggleColumnVisibility = (field: string, isVisible: boolean) => {
    setColumnVisibility((prev) => ({
      ...prev,
      [field]: isVisible,
    }));

    if (gridRef.current) {
      gridRef.current.api.setColumnsVisible([field], isVisible);
    }
  };

  // Add a custom message for no data available
  const noRowsOverlayTemplate = '<span class="ag-overlay-no-rows-center">No Data Available</span>';

  return (
    <div className={`animate-in fade-in duration-1000`}>
      <div className="flex justify-between w-full lg:w-full lg:flex lg:justify-end mb-3 animate-in fade-in duration-1000 gap-5">
        {showPageEntries && (
          <select
            value={page} // Set the value to current page size
            onChange={handlePageChange}
            className=" border-2 rounded-md  items-center justify-center cursor-pointer"
          >
            <option value={10}>10</option>
            <option value={15}>15</option>
            <option value={25}>25</option>
            <option value={50}>50</option>
            <option value={100}>100</option>
            <option value={250}>250</option>
            <option value={500}>500</option>
            <option value={1000}>1000</option>
            <option value={1500}>1500</option>
            <option value={2000}>2000</option>
          </select>
        )}

        {showColDropDowns && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <p className="p-2 border-2 rounded-md flex items-center justify-center px-3 cursor-pointer">
                <FaFilter />
              </p>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="bg-white border-none dark:bg-gray-900 dark:ring-1 dark:ring-gray-800"
              onSelect={(e) => e.preventDefault()}
            >
              {columnsWithSerialNumber
                .filter((column) => column.hideable !== false)
                .map((column) => (
                  <DropdownMenuCheckboxItem
                    key={column.field}
                    className="capitalize cursor-pointer"
                    checked={columnVisibility[column.field]}
                    onCheckedChange={(value) =>
                      toggleColumnVisibility(column.field, value)
                    }
                    onSelect={(e) => e.preventDefault()}
                  >
                    {column.headerName || column.field}
                  </DropdownMenuCheckboxItem>
                ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {showSearchColumn && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <p className="p-2 border-2 rounded-md flex items-center justify-center px-3 cursor-pointer">
                <FaSearch />
              </p>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="bg-white border-none dark:bg-gray-900 dark:ring-1 dark:ring-gray-800"
              onSelect={(e) => e.preventDefault()}
            >
              <div className="px-3 py-2">
                <p className="font-semibold text-sm">Select Columns</p>
                {columnsWithSerialNumber
                  .filter(
                    (item: any) =>
                      item.field !== "action" &&
                      item.field !== "Activity Log" &&
                      item.field !== "pause" &&
                      item.field !== "finish_time" &&
                      item.field !== "start_time" &&
                      item.field !== "time_spent" &&
                      item.field !== "sr_no" &&
                      item.field !== "payment terms" &&
                      item.field !== "stableId"
                  )
                  .map((item: any, id: any) => {
                    return (
                      <DropdownMenuCheckboxItem
                        key={id}
                        checked={selectedColumns.includes(item.field)}
                        onCheckedChange={() =>
                          handleColumnSelection(item.field, item.headerName)
                        }
                        className="capitalize cursor-pointer"
                        onSelect={(e) => e.preventDefault()}
                      >
                        {item.headerName}
                      </DropdownMenuCheckboxItem>
                    );
                  })}
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {filterColumns.length > 0 && (
          <>
            {filterColumns.map((col, index) =>
              col?.field === "date" ? (
                <div key={index} className="w-full md:w-auto">
                  {filter3view}
                </div>
              ) : (
                <div
                  key={index}
                  className="flex flex- w-full md:w-[calc(20%-1rem)] min-w-[100px]"
                >
                  <div className="relative">
                    <div className="flex flex-wrap items-center gap-1 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md  px-1 focus-within:ring-0 focus-within:ring-blue-600 focus-within:border-blue-600 shadow-sm">
                      {/* Selected terms as tags inside input */}
                      {searchTerms[col.headerName]?.length > 0 && (
                        <>
                          {searchTerms[col.headerName].map((term, i) => (
                            <div
                              key={i}
                              className=" flex items-center bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-2 rounded-full text-xs"
                            >
                              <span className="mr-1 truncate max-w-xs">
                                {term}
                              </span>
                              <button
                                onClick={(e) => {
                                  e.preventDefault();
                                  handleRemoveTerm(col.headerName, term);
                                }}
                                className="  text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-100"
                              >
                                ×
                              </button>
                            </div>
                          ))}
                        </>
                      )}
                      <CustomInput
                        value={inputValues[col.headerName] || ""}
                        onChange={(e) =>
                          setInputValues({
                            ...inputValues,
                            [col.headerName]: e.target.value,
                          })
                        }
                        onKeyDown={(e) => handleKeyDown(e, col.headerName)}
                        placeholder={
                          searchTerms[col.headerName]?.length > 0
                            ? ""
                            : `Search ${col.headerName}...`
                        }
                        className="flex-1 min-w-[30px] bg-transparent border-none focus:outline-none"
                      />
                    </div>
                  </div>
                </div>
              )
            )}
          </>
        )}
        {filter && filterColumns.length > 0 && (
          <>
            {filterColumns.map((column: any, index: number) => {
              <Input
                key={index}
                placeholder={
                  filter1PlaceHolder ? filter1PlaceHolder : filter_column
                }
                value={inputValues[column.headerName] || ""}
                onChange={(e) =>
                  setInputValues({
                    ...inputValues,
                    [column.headerName]: e.target.value,
                  })
                }
                onKeyDown={(e) => handleKeyDown(e, column.headerName)}
                className="w-[20%] dark:bg-gray-700 !outline-main-color"
              />;
            })}
          </>
        )}
        {filter2 && (
          <>
            {filterColumns.map((column: any, index: number) => {
              <Input
                key={index}
                placeholder={`${filter_column2}`}
                value={inputValues[column.headerName] || ""}
                onChange={(e) =>
                  setInputValues({
                    ...inputValues,
                    [column.headerName]: e.target.value,
                  })
                }
                onKeyDown={(e) => handleKeyDown(e, column.headerName)}
                className="w-[20%] dark:bg-gray-700 !outline-main-color"
              />;
            })}
          </>
        )}
      </div>

      {total && <div>{totalview}</div>}

      <div className={cn("", className)}>
        <div
          className="ag-theme-alpine custom-ag-grid"
          style={{ height: "100vh", width: "100%" }}
        >
          <AgGridReact
            ref={gridRef}
            rowData={processedData}
            columnDefs={columnsWithSerialNumber}
            headerHeight={35}
            overlayNoRowsTemplate={noRowsOverlayTemplate}
            onGridReady={(params) => {
              params.api.sizeColumnsToFit();
              // Show overlays on grid ready
              if (isLoading) {
                params.api.showLoadingOverlay();
              } else if (!processedData || processedData.length === 0) {
                params.api.showNoRowsOverlay();
              } else {
                params.api.hideOverlay();
              }
            }}
            onFirstDataRendered={(params) => {
              params.api.sizeColumnsToFit();
            }}
            onColumnVisible={(event) => {
              event.api.sizeColumnsToFit();
            }}
            onGridSizeChanged={(params) => {
              params.api.sizeColumnsToFit();
            }}
            defaultColDef={{
              sortable: true,
              resizable: true,
              cellStyle: { borderRight: "1px solid #ddd" },
            }}
          />
        </div>
      </div>

      {data && (
        <Pagination
          currentPage={
            totalPages
              ? Number(params.get("page")) || 1
              : (gridRef.current?.api?.paginationGetCurrentPage() || 0) + 1
          }
          totalPages={
            totalPages
              ? totalPages
              : gridRef.current?.api?.paginationGetTotalPages() || 1
          }
          onPageChange={(page: number) => {
            if (gridRef.current) {
              gridRef?.current?.api?.paginationGoToPage(page - 1);
            }

            if (totalPages) {
              params.set("page", page.toString());
              replace(`${pathname}?${params.toString()}`);
            }
          }}
        />
      )}
    </div>
  );
};

export default DataGridTable;

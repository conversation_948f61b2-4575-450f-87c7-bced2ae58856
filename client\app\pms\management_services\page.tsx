"use client";
import React, { useState, useMemo, useContext } from "react";
import { useSearchParams } from "next/navigation";
import {
  FaUserPlus,
  FaUserCircle,
  FaTruck,
  FaBuilding,
  FaShieldAlt,
  FaFileSignature,
  FaBriefcase,
  FaPenFancy,
  FaGripHorizontal,
  FaStream,
  FaChevronDown,
} from "react-icons/fa";
import { BiSolidCategory } from "react-icons/bi";
import { RiFileAddLine, RiUserFill } from "react-icons/ri";
import { TbReportSearch, TbTicket } from "react-icons/tb";
import { colorThemes } from "@/components/ui/colorTheme";
import { FaFileArrowDown, FaRegFolderOpen } from "react-icons/fa6";
import { BsFillFileEarmarkSpreadsheetFill } from "react-icons/bs";
import { HiChevronDown } from "react-icons/hi";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Activity,
  TrendingUp,
  Clock,
  Star,
  Users,
  FileText,
  BarChart3,
  Settings,
  Shield,
  Truck,
  Grid3X3,
  Bell,
  Search,
  FileSpreadsheetIcon,
  FileUpIcon,
} from "lucide-react";
import { TicketContext } from "../manage_tickets/TicketContext";

// --- Real Routes and Permission Logic ---
const Routes = [
  // User & Roles Management
  {
    label: "Manage User",
    path: "/pms/manage_employee",
    permission: { module: "USER MANAGEMENT" },
    icon: Users,
    category: "User & Roles Management",
  },
  {
    label: "Manage Roles",
    path: "/pms/manage-roles",
    permission: { module: "ROLE MANAGEMENT" },
    icon: Shield,
    category: "User & Roles Management",
  },
  {
    label: "Manage Associate",
    path: "/pms/manage_associate",
    permission: { module: "ASSOCIATE MANAGEMENT" },
    icon: Users,
    category: "User & Roles Management",
  },
  // Organization Management
  {
    label: "Manage Branch",
    path: "/pms/manage_branch",
    permission: { module: "BRANCH MANAGEMENT" },
    icon: FaBuilding,
    category: "Organization Management",
  },
  {
    label: "Manage Client",
    path: "/pms/manage_client",
    permission: { module: "CLIENT MANAGEMENT", action: "create-client" },
    icon: Users,
    category: "Organization Management",
  },
  {
    label: "Manage Carrier",
    path: "/pms/manage_carrier",
    permission: { module: "CARRIER MANAGEMENT" },
    icon: Truck,
    category: "Organization Management",
  },
  {
    label: "Manage Category",
    path: "/pms/manage_category",
    permission: { module: "CATEGORY MANAGEMENT" },
    icon: Grid3X3,
    category: "Organization Management",
  },
  // Customizations
  {
    label: "Add/Update Custom Fields",
    path: "/pms/addupdate_custom_fields",
    permission: { module: "CUSTOM FIELD MANAGEMENT" },
    icon: FaPenFancy,
    category: "Customizations",
  },
  {
    label: "Arrange Custom Fields",
    path: "/pms/arrange_custom_fields",
    permission: { module: "CUSTOM FIELD ARRANGEMENT" },
    icon: FaGripHorizontal,
    category: "Customizations",
  },
  {
    label: "Manage File Path",
    path: "/pms/manage_file_path",
    permission: { module: "FILE PATH MANAGEMENT" },
    icon: FaFileArrowDown,
    category: "Customizations",
  },
  // Operations
  {
    label: "Manage Work Type",
    path: "/pms/manage_work_type",
    permission: { module: "WORKTYPE MANAGEMENT", action: "update-work" },
    icon: FaBriefcase,
    category: "Operations",
  },
  {
    label: "Manage Work Report",
    path: "/pms/manage_work_report",
    permission: { module: "WORK REPORT" },
    icon: Activity,
    category: "Operations",
  },
  {
    label: "Manage Report",
    path: "/pms/customize_report",
    permission: { module: "CUSTOMIZE REPORT" },
    icon: FileText,
    category: "Operations",
  },
  {
    label: "TrackSheets",
    path: "/pms/track_sheets",
    permission: { module: "TRACKSHEET REPORT" },
    icon: BarChart3,
    category: "Operations",
  },
  {
    label: "Manage Imported Files",
    path: "/user/trackSheets/imported-files",
    permission: { module: "IMPORTED FILES MANAGEMENT" },
    icon: FileUpIcon,
    category: "Operations",
  },
  {
    label: "Manage Pipelines",
    path: "/pms/manage_pipelines",
    permission: { module: "PIPELINE MANAGEMENT" },
    icon: FaStream,
    category: "Customizations",
  },
  {
    label: "Manage Tickets",
    path: "/pms/manage_tickets",
    permission: { module: "TICKET MANAGEMENT" },
    icon: TbTicket,
    category: "Operations",
  },
  {
    label: "Manage Legrand Mappings",
    path: "/pms/manage_legrand_mappings",
    permission: { module: "LEGRAND MAPPING" },
    icon: RiFileAddLine,
    category: "Operations",
  },
  // {
  //   label: "Manage Invoice Files",
  //   path: "/pms/manage_invoice_files",
  //   permission: { module: "INVOICE FILE MANAGEMENT" },
  //   icon: FileSpreadsheetIcon,
  //   category: "Operations",
  // }
];

const categoryOrder = [
  "User & Roles Management",
  "Organization Management",
  "Customizations",
  "Operations",
];

const categoryIcons = {
  "User & Roles Management": Users,
  "Organization Management": FaBuilding,
  Customizations: FaPenFancy,
  Operations: FaStream,
};

const categoryColors = {
  "User & Roles Management": "bg-blue-500",
  "Organization Management": "bg-orange-500",
  Customizations: "bg-purple-500",
  Operations: "bg-green-500",
};

// Add helper to get recent service paths from localStorage
function getRecentServicePaths() {
  try {
    return JSON.parse(localStorage.getItem("recentServices") || "[]");
  } catch {
    return [];
  }
}

const ManagementServicesPage = () => {
  const { currentUser } = useContext(TicketContext);
  const userKey = currentUser?.id || currentUser?.username || "guest";
  const RECENTS_KEY = `recentServices_${userKey}`;

  function getRecentServicePaths() {
    try {
      return JSON.parse(localStorage.getItem(RECENTS_KEY) || "[]");
    } catch {
      return [];
    }
  }
  function setRecentServicePaths(recents) {
    localStorage.setItem(RECENTS_KEY, JSON.stringify(recents));
  }

  const FAVORITES_KEY = `favoriteServices_${userKey}`;
  function getFavoriteServicePaths() {
    try {
      return JSON.parse(localStorage.getItem(FAVORITES_KEY) || "[]");
    } catch {
      return [];
    }
  }
  function setFavoriteServicePaths(favorites) {
    localStorage.setItem(FAVORITES_KEY, JSON.stringify(favorites));
  }

  // Dummy state to force re-render on favorite toggle
  const [dummyState, setDummyState] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const [permissions, setPermissions] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all");

  React.useLayoutEffect(() => {
    setIsClient(true);
    const storedPermissions = sessionStorage.getItem("permissions");
    if (storedPermissions) {
      try {
        const parsedPermissions = JSON.parse(storedPermissions);
        const validPermissions = parsedPermissions.filter(
          (perm: any) => perm !== null
        );
        setPermissions(
          validPermissions.length > 0 ? validPermissions : ["admin"]
        );
      } catch (error) {
        setPermissions(["admin"]);
      }
    } else {
      setPermissions(["admin"]);
    }
  }, []);

  if (!isClient) return null;

  const hasAllowAllPermission = permissions.includes("allow_all");
  const filteredRoutes = hasAllowAllPermission
    ? Routes
    : permissions.includes("admin")
    ? Routes
    : Routes.filter((route) =>
        permissions.some(
          (permission) =>
            typeof permission === "string" &&
            permission === route.permission.module
        )
      );

  // Group routes by category
  const groupedRoutes = filteredRoutes.reduce((acc: any, route) => {
    if (!acc[route.category]) acc[route.category] = [];
    acc[route.category].push(route);
    return acc;
  }, {});

  // Filtered grouped routes by search
  const filteredGroupedRoutes = Object.fromEntries(
    Object.entries(groupedRoutes).map(([category, routes]) => [
      category,
      (routes as any[]).filter((route: any) =>
        route.label.toLowerCase().includes(searchQuery.toLowerCase())
      ),
    ])
  );

  // All services for tabs
  const allServices = filteredRoutes.map((route) => ({
    ...route,
    category: route.category,
  }));

  // Real recently used services
  const recentServices = getRecentServicePaths()
    .map((path) => allServices.find((s) => s.path === path))
    .filter(Boolean);

  // Real favorite services
  const favoriteServicePaths = getFavoriteServicePaths();
  const favoriteServices = favoriteServicePaths
    .map((path) => allServices.find((s) => s.path === path))
    .filter(Boolean);

  // Handler to update recent services and navigate
  const handleServiceClick = (service) => {
    let recent = [];
    try {
      recent = JSON.parse(localStorage.getItem(RECENTS_KEY) || "[]");
    } catch {}
    recent = recent.filter((id) => id !== service.path);
    recent.unshift(service.path);
    if (recent.length > 6) recent = recent.slice(0, 6);
    localStorage.setItem(RECENTS_KEY, JSON.stringify(recent));
    window.location.href = service.path;
  };

  // Handler to toggle favorite status
  const handleFavoriteClick = (service, e) => {
    e.stopPropagation();
    let favorites = getFavoriteServicePaths();
    if (favorites.includes(service.path)) {
      favorites = favorites.filter((id) => id !== service.path);
    } else {
      favorites.unshift(service.path);
      if (favorites.length > 12) favorites = favorites.slice(0, 12);
    }
    setFavoriteServicePaths(favorites);
    // Force re-render by using a dummy state
    setDummyState((s) => !s);
  };

  // Check if any category has visible routes after filtering
  const hasVisibleRoutes = Object.values(filteredGroupedRoutes).some(
    (routes: any) => routes.length > 0
  );

  return (
    <div className="min-h-screen bg-gray-50/50 p-4 md:p-6 lg:p-8">
      <div className="mx-auto max-w-7xl space-y-8">
        {/* Header Section */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold tracking-tight text-gray-900">
            Management Services
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Centralized command center for all administrative modules and
            operations
          </p>
        </div>

        {/* Search Bar */}
        <div className="relative max-w-2xl mx-auto">
          <Input
            type="text"
            placeholder="Search services, features, or modules..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-12 pr-4 py-3 text-lg border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-0 shadow-sm"
          />
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
        </div>
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-6"
        >
          <TabsList className="grid w-full grid-cols-4 lg:w-fit lg:grid-cols-4">
            <TabsTrigger value="all">All Services</TabsTrigger>
            <TabsTrigger value="recent">Recent</TabsTrigger>
            <TabsTrigger value="favorites">Favorites</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
          </TabsList>

          {/* All Services Tab */}
          <TabsContent value="all" className="space-y-8">
            {categoryOrder.map((categoryName) =>
              filteredGroupedRoutes[categoryName]?.length > 0 ? (
                <div
                  key={categoryName}
                  className="space-y-4"
                  id={categoryName.replace(/\s+/g, "-")}
                >
                  <div className="flex items-center gap-3">
                    <div
                      className={`p-2 rounded-lg ${categoryColors[categoryName]} text-white`}
                    >
                      {React.createElement(
                        categoryIcons[categoryName] || Users,
                        { className: "h-5 w-5" }
                      )}
                    </div>
                    <h2 className="text-xl font-semibold text-gray-900">
                      {categoryName}
                    </h2>
                    <Badge variant="secondary">
                      {filteredGroupedRoutes[categoryName].length}
                    </Badge>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    {filteredGroupedRoutes[categoryName].map((service: any) => (
                      <Card
                        key={service.path}
                        className="group hover:shadow-xl hover:-translate-y-1 transition-all duration-300 cursor-pointer border-0 shadow-md hover:shadow-blue-100"
                        onClick={() => handleServiceClick(service)}
                      >
                        <CardHeader className="pb-3 relative">
                          <div className="flex flex-col items-center justify-center">
                            <div className="p-2 rounded-lg bg-gray-100 group-hover:bg-blue-100 transition-colors mb-2">
                              {React.createElement(service.icon || Users, {
                                className:
                                  "h-5 w-5 text-gray-600 group-hover:text-blue-600",
                              })}
                            </div>
                            <CardTitle className="text-lg group-hover:text-blue-600 transition-colors text-center">
                              {service.label}
                            </CardTitle>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="opacity-0 group-hover:opacity-100 transition-opacity absolute top-2 right-2"
                            onClick={(e) => handleFavoriteClick(service, e)}
                          >
                            {favoriteServicePaths.includes(service.path) ? (
                              <Star
                                className="h-4 w-4 text-yellow-500 fill-current"
                                fill="currentColor"
                              />
                            ) : (
                              <Star className="h-4 w-4" />
                            )}
                          </Button>
                        </CardHeader>
                        <CardContent className="pt-0">
                          {/*<CardDescription className="text-sm text-gray-600">
                            {service.path}
                          </CardDescription>*/}
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              ) : null
            )}
          </TabsContent>

          {/* Recent Tab */}
          <TabsContent value="recent" className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-semibold text-gray-900">
                Recently Used
              </h2>
              <Button variant="outline" size="sm">
                <Clock className="h-4 w-4 mr-2" />
                Clear History
              </Button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {recentServices.map((service) => (
                <Card
                  key={service.path}
                  className="group hover:shadow-lg transition-all duration-200 cursor-pointer"
                  onClick={() => handleServiceClick(service)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex flex-col items-center justify-center">
                      <div className="p-2 rounded-lg bg-gray-100 group-hover:bg-blue-100 transition-colors mb-2">
                        {React.createElement(service.icon || Users, {
                          className:
                            "h-5 w-5 text-gray-600 group-hover:text-blue-600",
                        })}
                      </div>
                      <CardTitle className="text-base group-hover:text-blue-600 transition-colors text-center">
                        {service.label}
                      </CardTitle>
                      <p className="text-xs text-gray-500 text-center">
                        {service.category}
                      </p>
                    </div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Favorites Tab */}
          <TabsContent value="favorites" className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-semibold text-gray-900">
                Favorite Services
              </h2>
              <Badge variant="secondary">
                {favoriteServices.length} favorites
              </Badge>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {favoriteServices.map((service) => (
                <Card
                  key={service.path}
                  className="group hover:shadow-lg transition-all duration-200 cursor-pointer border-yellow-200"
                  onClick={() => handleServiceClick(service)}
                >
                  <CardHeader className="pb-3 relative">
                    <div className="flex flex-col items-center justify-center">
                      <div className="p-2 rounded-lg bg-yellow-100 group-hover:bg-yellow-200 transition-colors mb-2">
                        {React.createElement(service.icon || Users, {
                          className: "h-5 w-5 text-yellow-600",
                        })}
                      </div>
                      <CardTitle className="text-base group-hover:text-yellow-600 transition-colors text-center">
                        {service.label}
                      </CardTitle>
                      <p className="text-xs text-gray-500 text-center">
                        {service.category}
                      </p>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="opacity-0 group-hover:opacity-100 transition-opacity absolute top-2 right-2"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleFavoriteClick(service, e);
                      }}
                    >
                      {favoriteServicePaths.includes(service.path) ? (
                        <Star
                          className="h-4 w-4 text-yellow-500 fill-current"
                          fill="currentColor"
                        />
                      ) : (
                        <Star className="h-4 w-4" />
                      )}
                    </Button>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Categories Tab */}
          <TabsContent value="categories" className="space-y-6">
            <h2 className="text-2xl font-semibold text-gray-900">
              Service Categories
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {categoryOrder.map((categoryName) =>
                groupedRoutes[categoryName]?.length > 0 ? (
                  <Card
                    key={categoryName}
                    className="group hover:shadow-lg transition-all duration-200 cursor-pointer"
                    onClick={() => {
                      setActiveTab("all");
                      setTimeout(() => {
                        const el = document.getElementById(
                          categoryName.replace(/\s+/g, "-")
                        );
                        if (el) {
                          el.scrollIntoView({
                            behavior: "smooth",
                            block: "start",
                          });
                        }
                      }, 50); // slight delay to ensure tab content is rendered
                    }}
                  >
                    <CardHeader>
                      <div className="flex items-center gap-4">
                        <div
                          className={`p-3 rounded-xl ${categoryColors[categoryName]} text-white group-hover:scale-110 transition-transform`}
                        >
                          {React.createElement(
                            categoryIcons[categoryName] || Users,
                            { className: "h-6 w-6" }
                          )}
                        </div>
                        <div>
                          <CardTitle className="text-lg group-hover:text-blue-600 transition-colors">
                            {categoryName}
                          </CardTitle>
                          <p className="text-sm text-gray-500">
                            {groupedRoutes[categoryName].length} services
                          </p>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {groupedRoutes[categoryName]
                          .slice(0, 3)
                          .map((service: any) => (
                            <div
                              key={service.path}
                              className="flex items-center gap-2 text-sm text-gray-600"
                            >
                              <div className="w-1.5 h-1.5 rounded-full bg-gray-300"></div>
                              {service.label}
                            </div>
                          ))}
                        {groupedRoutes[categoryName].length > 3 && (
                          <p className="text-xs text-gray-500">
                            +{groupedRoutes[categoryName].length - 3} more
                            services
                          </p>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ) : null
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default ManagementServicesPage;

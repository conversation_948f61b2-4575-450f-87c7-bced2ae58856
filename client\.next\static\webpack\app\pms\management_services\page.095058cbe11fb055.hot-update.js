"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pms/management_services/page",{

/***/ "(app-pages-browser)/./app/pms/management_services/page.tsx":
/*!**********************************************!*\
  !*** ./app/pms/management_services/page.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FaBriefcase_FaBuilding_FaGripHorizontal_FaPenFancy_FaStream_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=FaBriefcase,FaBuilding,FaGripHorizontal,FaPenFancy,FaStream!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_RiFileAddLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=RiFileAddLine!=!react-icons/ri */ \"(app-pages-browser)/./node_modules/react-icons/ri/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_TbTicket_react_icons_tb__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=TbTicket!=!react-icons/tb */ \"(app-pages-browser)/./node_modules/react-icons/tb/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_FaFileArrowDown_react_icons_fa6__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=FaFileArrowDown!=!react-icons/fa6 */ \"(app-pages-browser)/./node_modules/react-icons/fa6/index.mjs\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,FileText,FileUpIcon,Grid3X3,Search,Shield,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,FileText,FileUpIcon,Grid3X3,Search,Shield,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,FileText,FileUpIcon,Grid3X3,Search,Shield,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,FileText,FileUpIcon,Grid3X3,Search,Shield,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,FileText,FileUpIcon,Grid3X3,Search,Shield,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,FileText,FileUpIcon,Grid3X3,Search,Shield,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,FileText,FileUpIcon,Grid3X3,Search,Shield,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,FileText,FileUpIcon,Grid3X3,Search,Shield,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,FileText,FileUpIcon,Grid3X3,Search,Shield,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,FileText,FileUpIcon,Grid3X3,Search,Shield,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,BarChart3,Clock,FileText,FileUpIcon,Grid3X3,Search,Shield,Star,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _manage_tickets_TicketContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../manage_tickets/TicketContext */ \"(app-pages-browser)/./app/pms/manage_tickets/TicketContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// --- Real Routes and Permission Logic ---\nconst Routes = [\n    // User & Roles Management\n    {\n        label: \"Manage User\",\n        path: \"/pms/manage_employee\",\n        permission: {\n            module: \"USER MANAGEMENT\"\n        },\n        icon: _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        category: \"User & Roles Management\"\n    },\n    {\n        label: \"Manage Roles\",\n        path: \"/pms/manage-roles\",\n        permission: {\n            module: \"ROLE MANAGEMENT\"\n        },\n        icon: _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        category: \"User & Roles Management\"\n    },\n    {\n        label: \"Manage Associate\",\n        path: \"/pms/manage_associate\",\n        permission: {\n            module: \"ASSOCIATE MANAGEMENT\"\n        },\n        icon: _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        category: \"User & Roles Management\"\n    },\n    // Organization Management\n    {\n        label: \"Manage Branch\",\n        path: \"/pms/manage_branch\",\n        permission: {\n            module: \"BRANCH MANAGEMENT\"\n        },\n        icon: _barrel_optimize_names_FaBriefcase_FaBuilding_FaGripHorizontal_FaPenFancy_FaStream_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaBuilding,\n        category: \"Organization Management\"\n    },\n    {\n        label: \"Manage Client\",\n        path: \"/pms/manage_client\",\n        permission: {\n            module: \"CLIENT MANAGEMENT\",\n            action: \"create-client\"\n        },\n        icon: _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        category: \"Organization Management\"\n    },\n    {\n        label: \"Manage Carrier\",\n        path: \"/pms/manage_carrier\",\n        permission: {\n            module: \"CARRIER MANAGEMENT\"\n        },\n        icon: _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        category: \"Organization Management\"\n    },\n    {\n        label: \"Manage Category\",\n        path: \"/pms/manage_category\",\n        permission: {\n            module: \"CATEGORY MANAGEMENT\"\n        },\n        icon: _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        category: \"Organization Management\"\n    },\n    // Customizations\n    {\n        label: \"Add/Update Custom Fields\",\n        path: \"/pms/addupdate_custom_fields\",\n        permission: {\n            module: \"CUSTOM FIELD MANAGEMENT\"\n        },\n        icon: _barrel_optimize_names_FaBriefcase_FaBuilding_FaGripHorizontal_FaPenFancy_FaStream_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaPenFancy,\n        category: \"Customizations\"\n    },\n    {\n        label: \"Arrange Custom Fields\",\n        path: \"/pms/arrange_custom_fields\",\n        permission: {\n            module: \"CUSTOM FIELD ARRANGEMENT\"\n        },\n        icon: _barrel_optimize_names_FaBriefcase_FaBuilding_FaGripHorizontal_FaPenFancy_FaStream_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaGripHorizontal,\n        category: \"Customizations\"\n    },\n    {\n        label: \"Manage File Path\",\n        path: \"/pms/manage_file_path\",\n        permission: {\n            module: \"FILE PATH MANAGEMENT\"\n        },\n        icon: _barrel_optimize_names_FaFileArrowDown_react_icons_fa6__WEBPACK_IMPORTED_MODULE_13__.FaFileArrowDown,\n        category: \"Customizations\"\n    },\n    // Operations\n    {\n        label: \"Manage Work Type\",\n        path: \"/pms/manage_work_type\",\n        permission: {\n            module: \"WORKTYPE MANAGEMENT\",\n            action: \"update-work\"\n        },\n        icon: _barrel_optimize_names_FaBriefcase_FaBuilding_FaGripHorizontal_FaPenFancy_FaStream_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaBriefcase,\n        category: \"Operations\"\n    },\n    {\n        label: \"Manage Work Report\",\n        path: \"/pms/manage_work_report\",\n        permission: {\n            module: \"WORK REPORT\"\n        },\n        icon: _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        category: \"Operations\"\n    },\n    {\n        label: \"Manage Report\",\n        path: \"/pms/customize_report\",\n        permission: {\n            module: \"CUSTOMIZE REPORT\"\n        },\n        icon: _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        category: \"Operations\"\n    },\n    {\n        label: \"TrackSheets\",\n        path: \"/pms/track_sheets\",\n        permission: {\n            module: \"TRACKSHEET REPORT\"\n        },\n        icon: _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        category: \"Operations\"\n    },\n    {\n        label: \"Manage Imported Files\",\n        path: \"/user/trackSheets/imported-files\",\n        permission: {\n            module: \"IMPORTED FILES MANAGEMENT\"\n        },\n        icon: _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n        category: \"Operations\"\n    },\n    {\n        label: \"Manage Pipelines\",\n        path: \"/pms/manage_pipelines\",\n        permission: {\n            module: \"PIPELINE MANAGEMENT\"\n        },\n        icon: _barrel_optimize_names_FaBriefcase_FaBuilding_FaGripHorizontal_FaPenFancy_FaStream_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaStream,\n        category: \"Customizations\"\n    },\n    {\n        label: \"Manage Tickets\",\n        path: \"/pms/manage_tickets\",\n        permission: {\n            module: \"TICKET MANAGEMENT\"\n        },\n        icon: _barrel_optimize_names_TbTicket_react_icons_tb__WEBPACK_IMPORTED_MODULE_18__.TbTicket,\n        category: \"Operations\"\n    },\n    {\n        label: \"Manage Legrand Mappings\",\n        path: \"/pms/manage_legrand_mappings\",\n        permission: {\n            module: \"LEGRAND MAPPING\"\n        },\n        icon: _barrel_optimize_names_RiFileAddLine_react_icons_ri__WEBPACK_IMPORTED_MODULE_19__.RiFileAddLine,\n        category: \"Operations\"\n    }\n];\nconst categoryOrder = [\n    \"User & Roles Management\",\n    \"Organization Management\",\n    \"Customizations\",\n    \"Operations\"\n];\nconst categoryIcons = {\n    \"User & Roles Management\": _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n    \"Organization Management\": _barrel_optimize_names_FaBriefcase_FaBuilding_FaGripHorizontal_FaPenFancy_FaStream_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaBuilding,\n    Customizations: _barrel_optimize_names_FaBriefcase_FaBuilding_FaGripHorizontal_FaPenFancy_FaStream_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaPenFancy,\n    Operations: _barrel_optimize_names_FaBriefcase_FaBuilding_FaGripHorizontal_FaPenFancy_FaStream_react_icons_fa__WEBPACK_IMPORTED_MODULE_10__.FaStream\n};\nconst categoryColors = {\n    \"User & Roles Management\": \"bg-blue-500\",\n    \"Organization Management\": \"bg-orange-500\",\n    Customizations: \"bg-purple-500\",\n    Operations: \"bg-green-500\"\n};\n// Add helper to get recent service paths from localStorage\nfunction getRecentServicePaths() {\n    try {\n        return JSON.parse(localStorage.getItem(\"recentServices\") || \"[]\");\n    } catch (e) {\n        return [];\n    }\n}\nconst ManagementServicesPage = ()=>{\n    _s();\n    const { currentUser } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_manage_tickets_TicketContext__WEBPACK_IMPORTED_MODULE_7__.TicketContext);\n    const userKey = (currentUser === null || currentUser === void 0 ? void 0 : currentUser.id) || (currentUser === null || currentUser === void 0 ? void 0 : currentUser.username) || \"guest\";\n    const RECENTS_KEY = \"recentServices_\".concat(userKey);\n    function getRecentServicePaths() {\n        try {\n            return JSON.parse(localStorage.getItem(RECENTS_KEY) || \"[]\");\n        } catch (e) {\n            return [];\n        }\n    }\n    function setRecentServicePaths(recents) {\n        localStorage.setItem(RECENTS_KEY, JSON.stringify(recents));\n    }\n    const FAVORITES_KEY = \"favoriteServices_\".concat(userKey);\n    function getFavoriteServicePaths() {\n        try {\n            return JSON.parse(localStorage.getItem(FAVORITES_KEY) || \"[]\");\n        } catch (e) {\n            return [];\n        }\n    }\n    function setFavoriteServicePaths(favorites) {\n        localStorage.setItem(FAVORITES_KEY, JSON.stringify(favorites));\n    }\n    // Dummy state to force re-render on favorite toggle\n    const [dummyState, setDummyState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    react__WEBPACK_IMPORTED_MODULE_1___default().useLayoutEffect(()=>{\n        setIsClient(true);\n        const storedPermissions = sessionStorage.getItem(\"permissions\");\n        if (storedPermissions) {\n            try {\n                const parsedPermissions = JSON.parse(storedPermissions);\n                const validPermissions = parsedPermissions.filter((perm)=>perm !== null);\n                setPermissions(validPermissions.length > 0 ? validPermissions : [\n                    \"admin\"\n                ]);\n            } catch (error) {\n                setPermissions([\n                    \"admin\"\n                ]);\n            }\n        } else {\n            setPermissions([\n                \"admin\"\n            ]);\n        }\n    }, []);\n    if (!isClient) return null;\n    const hasAllowAllPermission = permissions.includes(\"allow_all\");\n    const filteredRoutes = hasAllowAllPermission ? Routes : permissions.includes(\"admin\") ? Routes : Routes.filter((route)=>permissions.some((permission)=>typeof permission === \"string\" && permission === route.permission.module));\n    // Group routes by category\n    const groupedRoutes = filteredRoutes.reduce((acc, route)=>{\n        if (!acc[route.category]) acc[route.category] = [];\n        acc[route.category].push(route);\n        return acc;\n    }, {});\n    // Filtered grouped routes by search\n    const filteredGroupedRoutes = Object.fromEntries(Object.entries(groupedRoutes).map((param)=>{\n        let [category, routes] = param;\n        return [\n            category,\n            routes.filter((route)=>route.label.toLowerCase().includes(searchQuery.toLowerCase()))\n        ];\n    }));\n    // All services for tabs\n    const allServices = filteredRoutes.map((route)=>({\n            ...route,\n            category: route.category\n        }));\n    // Real recently used services\n    const recentServices = getRecentServicePaths().map((path)=>allServices.find((s)=>s.path === path)).filter(Boolean);\n    // Real favorite services\n    const favoriteServicePaths = getFavoriteServicePaths();\n    const favoriteServices = favoriteServicePaths.map((path)=>allServices.find((s)=>s.path === path)).filter(Boolean);\n    // Handler to update recent services and navigate\n    const handleServiceClick = (service)=>{\n        let recent = [];\n        try {\n            recent = JSON.parse(localStorage.getItem(RECENTS_KEY) || \"[]\");\n        } catch (e) {}\n        recent = recent.filter((id)=>id !== service.path);\n        recent.unshift(service.path);\n        if (recent.length > 6) recent = recent.slice(0, 6);\n        localStorage.setItem(RECENTS_KEY, JSON.stringify(recent));\n        window.location.href = service.path;\n    };\n    // Handler to toggle favorite status\n    const handleFavoriteClick = (service, e)=>{\n        e.stopPropagation();\n        let favorites = getFavoriteServicePaths();\n        if (favorites.includes(service.path)) {\n            favorites = favorites.filter((id)=>id !== service.path);\n        } else {\n            favorites.unshift(service.path);\n            if (favorites.length > 12) favorites = favorites.slice(0, 12);\n        }\n        setFavoriteServicePaths(favorites);\n        // Force re-render by using a dummy state\n        setDummyState((s)=>!s);\n    };\n    // Check if any category has visible routes after filtering\n    const hasVisibleRoutes = Object.values(filteredGroupedRoutes).some((routes)=>routes.length > 0);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50/50 p-4 md:p-6 lg:p-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mx-auto max-w-7xl space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold tracking-tight text-gray-900\",\n                            children: \"Management Services\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                            children: \"Centralized command center for all administrative modules and operations\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                    lineNumber: 366,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative max-w-2xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                            type: \"text\",\n                            placeholder: \"Search services, features, or modules...\",\n                            value: searchQuery,\n                            onChange: (e)=>setSearchQuery(e.target.value),\n                            className: \"pl-12 pr-4 py-3 text-lg border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-0 shadow-sm\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            className: \"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                    lineNumber: 377,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                            className: \"grid w-full grid-cols-4 lg:w-fit lg:grid-cols-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"all\",\n                                    children: \"All Services\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"recent\",\n                                    children: \"Recent\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"favorites\",\n                                    children: \"Favorites\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"categories\",\n                                    children: \"Categories\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"all\",\n                            className: \"space-y-8\",\n                            children: categoryOrder.map((categoryName)=>{\n                                var _filteredGroupedRoutes_categoryName;\n                                return ((_filteredGroupedRoutes_categoryName = filteredGroupedRoutes[categoryName]) === null || _filteredGroupedRoutes_categoryName === void 0 ? void 0 : _filteredGroupedRoutes_categoryName.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    id: categoryName.replace(/\\s+/g, \"-\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-2 rounded-lg \".concat(categoryColors[categoryName], \" text-white\"),\n                                                    children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(categoryIcons[categoryName] || _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                    children: categoryName\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                    variant: \"secondary\",\n                                                    children: filteredGroupedRoutes[categoryName].length\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4\",\n                                            children: filteredGroupedRoutes[categoryName].map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                    className: \"group hover:shadow-xl hover:-translate-y-1 transition-all duration-300 cursor-pointer border-0 shadow-md hover:shadow-blue-100\",\n                                                    onClick: ()=>handleServiceClick(service),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                            className: \"pb-3 relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col items-center justify-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"p-2 rounded-lg bg-gray-100 group-hover:bg-blue-100 transition-colors mb-2\",\n                                                                            children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(service.icon || _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                                className: \"h-5 w-5 text-gray-600 group-hover:text-blue-600\"\n                                                                            })\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                            lineNumber: 433,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                                            className: \"text-lg group-hover:text-blue-600 transition-colors text-center\",\n                                                                            children: service.label\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                            lineNumber: 439,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    className: \"opacity-0 group-hover:opacity-100 transition-opacity absolute top-2 right-2\",\n                                                                    onClick: (e)=>handleFavoriteClick(service, e),\n                                                                    children: favoriteServicePaths.includes(service.path) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-yellow-500 fill-current\",\n                                                                        fill: \"currentColor\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                        lineNumber: 450,\n                                                                        columnNumber: 31\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                        lineNumber: 455,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                    lineNumber: 443,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                            className: \"pt-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, service.path, true, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, categoryName, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 17\n                                }, undefined) : null;\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"recent\",\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-semibold text-gray-900\",\n                                            children: \"Recently Used\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                \"Clear History\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                    children: recentServices.map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"group hover:shadow-lg transition-all duration-200 cursor-pointer\",\n                                            onClick: ()=>handleServiceClick(service),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                className: \"pb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center justify-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-2 rounded-lg bg-gray-100 group-hover:bg-blue-100 transition-colors mb-2\",\n                                                            children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(service.icon || _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                className: \"h-5 w-5 text-gray-600 group-hover:text-blue-600\"\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                            className: \"text-base group-hover:text-blue-600 transition-colors text-center\",\n                                                            children: service.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 text-center\",\n                                                            children: service.category\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                lineNumber: 490,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, service.path, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"favorites\",\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-semibold text-gray-900\",\n                                            children: \"Favorite Services\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                            variant: \"secondary\",\n                                            children: [\n                                                favoriteServices.length,\n                                                \" favorites\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                    children: favoriteServices.map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"group hover:shadow-lg transition-all duration-200 cursor-pointer border-yellow-200\",\n                                            onClick: ()=>handleServiceClick(service),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                className: \"pb-3 relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-center justify-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-2 rounded-lg bg-yellow-100 group-hover:bg-yellow-200 transition-colors mb-2\",\n                                                                children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(service.icon || _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-yellow-600\"\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                lineNumber: 530,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                                className: \"text-base group-hover:text-yellow-600 transition-colors text-center\",\n                                                                children: service.label\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                lineNumber: 535,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 text-center\",\n                                                                children: service.category\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                lineNumber: 538,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        className: \"opacity-0 group-hover:opacity-100 transition-opacity absolute top-2 right-2\",\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            handleFavoriteClick(service, e);\n                                                        },\n                                                        children: favoriteServicePaths.includes(service.path) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4 text-yellow-500 fill-current\",\n                                                            fill: \"currentColor\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 25\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, service.path, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                            lineNumber: 512,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"categories\",\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold text-gray-900\",\n                                    children: \"Service Categories\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: categoryOrder.map((categoryName)=>{\n                                        var _groupedRoutes_categoryName;\n                                        return ((_groupedRoutes_categoryName = groupedRoutes[categoryName]) === null || _groupedRoutes_categoryName === void 0 ? void 0 : _groupedRoutes_categoryName.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"group hover:shadow-lg transition-all duration-200 cursor-pointer\",\n                                            onClick: ()=>{\n                                                setActiveTab(\"all\");\n                                                setTimeout(()=>{\n                                                    const el = document.getElementById(categoryName.replace(/\\s+/g, \"-\"));\n                                                    if (el) {\n                                                        el.scrollIntoView({\n                                                            behavior: \"smooth\",\n                                                            block: \"start\"\n                                                        });\n                                                    }\n                                                }, 50); // slight delay to ensure tab content is rendered\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 rounded-xl \".concat(categoryColors[categoryName], \" text-white group-hover:scale-110 transition-transform\"),\n                                                                children: /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(categoryIcons[categoryName] || _barrel_optimize_names_Activity_BarChart3_Clock_FileText_FileUpIcon_Grid3X3_Search_Shield_Star_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-6 w-6\"\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                lineNumber: 594,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                                        className: \"text-lg group-hover:text-blue-600 transition-colors\",\n                                                                        children: categoryName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                        lineNumber: 603,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-gray-500\",\n                                                                        children: [\n                                                                            groupedRoutes[categoryName].length,\n                                                                            \" services\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                        lineNumber: 606,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                lineNumber: 602,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                        lineNumber: 593,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            groupedRoutes[categoryName].slice(0, 3).map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 text-sm text-gray-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-1.5 h-1.5 rounded-full bg-gray-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                            lineNumber: 621,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        service.label\n                                                                    ]\n                                                                }, service.path, true, {\n                                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                    lineNumber: 617,\n                                                                    columnNumber: 29\n                                                                }, undefined)),\n                                                            groupedRoutes[categoryName].length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    \"+\",\n                                                                    groupedRoutes[categoryName].length - 3,\n                                                                    \" more services\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                                lineNumber: 626,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                        lineNumber: 613,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                                    lineNumber: 612,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, categoryName, true, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 19\n                                        }, undefined) : null;\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                            lineNumber: 567,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n                    lineNumber: 387,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n            lineNumber: 364,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\pms\\\\management_services\\\\page.tsx\",\n        lineNumber: 363,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ManagementServicesPage, \"5Yxm9tWB8B8/wl6mF3HJzTGcXbc=\");\n_c = ManagementServicesPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ManagementServicesPage);\nvar _c;\n$RefreshReg$(_c, \"ManagementServicesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/pms/management_services/page.tsx\n"));

/***/ })

});